<?php

namespace App\Mail\Traits;

trait HasDomainBranding
{
    /**
     * Get brand data from current domain
     */
    protected function getBrandData(): array
    {
        $brandConfig = null;
        $currentDomain = null;

        if (app()->has('current.domain')) {
            $currentDomain = app('current.domain');
            $brandConfig = $currentDomain->branding_config;
        }


        return [
            'brandName' => $brandConfig['name'] ?? config('app.name'),
            'brandSlogan' => $brandConfig['slogan'] ?? 'Thanh toán tự động & chia sẻ biến động số dư',
            'brandEmail' => $brandConfig['email'] ?? config('mail.from.address'),
            'brandLogoUrl' => $brandConfig['logo_url'] ?? null,
            'frontendUrl' => $currentDomain ? "https://{$currentDomain->frontend_hostname}" : config('app.url'),
        ];
    }
}
