@props([
    'title' => config('app.name'),
    'label' => 'Email',
    'styles' => '',
    'message' => null,
])

@php
    // Brand data is passed from Mailable classes via HasDomainBranding trait
    // Set fallback values if not provided
    $brandName = $brandName ?? config('app.name');
    $brandSlogan = $brandSlogan ?? 'Thanh toán tự động & chia sẻ biến động số dư';
    $brandEmail = $brandEmail ?? config('mail.from.address');
    $brandLogoUrl = $brandLogoUrl ?? null;
    $frontendUrl = $frontendUrl ?? config('app.url');
@endphp

<!DOCTYPE html>
<html
  lang="en"
  xmlns:v="urn:schemas-microsoft-com:vml"
  xmlns:o="urn:schemas-microsoft-com:office:office"
>

<head>
  <meta charset="utf-8">
  <meta name="x-apple-disable-message-reformatting">
  <meta
    http-equiv="x-ua-compatible"
    content="ie=edge"
  >
  <meta
    name="viewport"
    content="width=device-width, initial-scale=1"
  >
  <meta
    name="format-detection"
    content="telephone=no, date=no, address=no, email=no"
  >
  <!--[if mso]>
    <xml>
    <o:OfficeDocumentSettings>
        <o:PixelsPerInch>96</o:PixelsPerInch>
    </o:OfficeDocumentSettings>
    </xml>
    <style>
    td, th, div, p, a, h1, h2, h3, h4, h5, h6 {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
        mso-line-height-rule: exactly;
    }
    </style>
    <![endif]-->
  <title>{{ $title }}</title>
  <style>
    /* X design system */
    :root {
      --x-black: #000000;
      --x-white: #ffffff;
      --x-gray: #666666;
      --x-light-gray: #f5f5f5;
      --x-dark-gray: #e0e0e0;
      --x-border: #f3f4f6;

      /* Light mode colors */
      --x-bg-primary: #ffffff;
      --x-bg-secondary: #f0f0f0;
      --x-text-primary: #000000;
      --x-text-secondary: #666666;
      --x-border-color: #f3f4f6;
      --x-card-bg: #f5f5f5;
      --x-button-bg: #000000;
      --x-button-text: #ffffff;
      --x-primary: #007bff;
      --x-success: #28a745;
    }

    /* Dark mode colors */
    @media (prefers-color-scheme: dark) {
      :root {
        --x-bg-primary: #1a1a1a;
        --x-bg-secondary: #0d0d0d;
        --x-text-primary: #ffffff;
        --x-text-secondary: #b3b3b3;
        --x-border-color: #333333;
        --x-card-bg: #2a2a2a;
        --x-button-bg: #ffffff;
        --x-button-text: #000000;
        --x-primary: #0d6efd;
        --x-success: #198754;
      }
    }

    body {
      margin: 0;
      padding: 0;
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
      line-height: 1.6;
      color: var(--x-text-primary);
      background-color: var(--x-bg-secondary);
    }

    p {
      margin-top: 0;
    }

    .hover-underline:hover {
      text-decoration: underline !important;
    }

    .email-wrapper {
      width: 100%;
      padding: 20px 0;
      background-color: var(--x-bg-secondary);
    }

    .email-container {
      max-width: 600px;
      margin: 0 auto;
      background-color: var(--x-bg-primary);
      border-radius: 8px;
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
      overflow: hidden;
      border: 1px solid var(--x-border-color);
    }

    .email-header {
      padding: 36px 32px 28px;
      text-align: center;
      border-bottom: 1px solid var(--x-border-color);
    }

    .logo-text {
      font-size: 28px;
      font-weight: 700;
      color: var(--x-text-primary);
      text-decoration: none;
      letter-spacing: 1px;
      display: inline-block;
      transition: opacity 0.2s ease;
    }

    .logo-text:hover {
      opacity: 0.8;
      text-decoration: none;
    }

    .email-content {
      padding: 40px 32px;
      background-color: var(--x-bg-primary);
    }

    .email-footer {
      padding: 32px;
      text-align: center;
      border-top: 1px solid var(--x-border-color);
    }

    .button {
      display: inline-block;
      padding: 12px 24px;
      background-color: var(--x-button-bg);
      color: var(--x-button-text) !important;
      text-decoration: none;
      border-radius: 6px;
      font-weight: 500;
      font-size: 14px;
      line-height: 1.4;
      transition: opacity 0.2s ease;
    }

    .button:hover {
      opacity: 0.8;
    }

    .text-primary {
      color: var(--x-text-primary);
      font-weight: 600;
    }

    .text-secondary {
      color: var(--x-text-secondary);
      font-size: 14px;
    }

    .divider {
      height: 1px;
      background-color: var(--x-border-color);
      margin: 32px 0;
    }

    /* Logo display control */
    .light-logo {
      display: inline-block;
    }

    .dark-logo {
      display: none;
    }

    @media (prefers-color-scheme: dark) {
      .light-logo {
        display: none;
      }

      .dark-logo {
        display: inline-block;
      }
    }

    @media (max-width: 600px) {

      .email-header,
      .email-content,
      .email-footer {
        padding-left: 24px !important;
        padding-right: 24px !important;
      }

      .email-header {
        padding-top: 32px !important;
        padding-bottom: 24px !important;
      }

      .email-content {
        padding-top: 32px !important;
        padding-bottom: 32px !important;
      }

      .logo-text {
        font-size: 24px;
      }
    }

    {{ $styles }}
  </style>
</head>

<body>
  <div style="display: none;">{{ $title }}</div>
  <div
    role="article"
    aria-roledescription="email"
    aria-label="{{ $label }}"
    lang="en"
    class="email-wrapper"
  >
    <div class="email-container">
      <!-- Header -->
      <div class="email-header">
        <a
          href="{{ $frontendUrl }}"
          target="_blank"
        >
          @if($brandLogoUrl)
            <!-- Custom brand logo -->
            <img
              src="{{ $brandLogoUrl }}"
              width="156"
              alt="{{ $brandName }}"
              style="border: 0; max-width: 100%; line-height: 100%; vertical-align: middle;"
            >
          @else
            <!-- Default logos -->
            <!-- Light mode logo -->
            <img
              src="{{ $message ? $message->embed(public_path('images/logo-black.png')) : asset('images/logo-black.png') }}"
              width="156"
              alt="{{ $brandName }}"
              style="border: 0; max-width: 100%; line-height: 100%; vertical-align: middle;"
              class="light-logo"
            >
            <!-- Dark mode logo -->
            <img
              src="{{ $message ? $message->embed(public_path('images/logo-white.png')) : asset('images/logo-white.png') }}"
              width="156"
              alt="{{ $brandName }}"
              style="border: 0; max-width: 100%; line-height: 100%; vertical-align: middle;"
              class="dark-logo"
            >
          @endif
        </a>
      </div>

      <!-- Content -->
      <div class="email-content">
        {{ $slot }}
      </div>

      <!-- Footer -->
      <div class="email-footer">
        <div style="margin-bottom: 16px;">
          <strong style="color: var(--x-text-primary);">{{ strtoupper($brandName) }}</strong>
          <br>
          <span style="font-size: 12px; color: var(--x-text-secondary);">{{ $brandSlogan }}</span>
          <br>
          <a
            href="mailto:{{ $brandEmail }}"
            class="hover-underline"
            style="font-size: 12px; color: var(--x-text-secondary); text-decoration: none;"
          >{{ $brandEmail }}</a>
        </div>
      </div>
    </div>
  </div>
</body>

</html>
