{"name": "codetay/xvps", "type": "project", "description": "XVPS - Manage VPS easily", "require": {"php": "^8.4", "ext-openssl": "*", "aws/aws-sdk-php": "^3.3", "blade-ui-kit/blade-heroicons": "^2.6", "cloudflare/sdk": "^1.4", "endroid/qr-code": "^6.0", "google/apiclient": "^2.18", "guzzlehttp/guzzle": "^7.9", "hidehalo/nanoid-php": "^2.0", "http-interop/http-factory-guzzle": "^1.2", "intervention/image": "^3.11", "laravel/framework": "^12.0", "laravel/horizon": "^5.21.4", "laravel/nova": "^5.0", "laravel/reverb": "^1.0", "laravel/sanctum": "^4.0", "laravel/scout": "^10.15", "laravel/socialite": "^5.10.0", "laravel/tinker": "^2.8.1", "league/tactician": "^1.1", "lorisleiva/laravel-actions": "^2.9", "marcin-orlowski/laravel-api-response-builder": "^12.0", "meilisearch/meilisearch-php": "^1.14", "nwidart/laravel-modules": "^12.0", "outl1ne/nova-inline-text-field": "^3.0", "phpseclib/phpseclib": "^3.0.21", "predis/predis": "^2.2.0", "pusher/pusher-php-server": "^7.2.3", "robthree/twofactorauth": "^3.0.0", "saloonphp/laravel-plugin": "^3.0", "saloonphp/saloon": "^3.0", "spatie/laravel-activitylog": "^4.10", "spatie/laravel-backup": "^9.0", "spatie/laravel-ciphersweet": "^1.3.0", "spatie/laravel-data": "^4.7", "spatie/laravel-fractal": "^6.0", "spatie/laravel-model-flags": "^1.3", "spatie/laravel-permission": "^6.7", "spatie/laravel-query-builder": "^6.0", "spatie/laravel-ray": "^1.36", "spatie/laravel-responsecache": "^7.6", "spatie/laravel-schemaless-attributes": "^2.5", "spatie/laravel-settings": "^3.4", "spatie/laravel-tags": "^4.5", "spatie/laravel-typescript-transformer": "^2.5", "tightenco/ziggy": "^2.0"}, "require-dev": {"barryvdh/laravel-ide-helper": "^3.5", "driftingly/rector-laravel": "^2.0", "fakerphp/faker": "^1.23.0", "laravel/nova-devtool": "^1.8", "mockery/mockery": "^1.6.6", "nunomaduro/collision": "^8.0", "pestphp/pest": "^3.0", "phpunit/phpunit": "^11.0", "rector/rector": "^2.0", "spatie/laravel-ignition": "^2.3.1"}, "autoload": {"files": ["modules/Core/app/Helpers/Helper.php", "modules/Core/app/Helpers/ResponseHelper.php", "modules/Core/app/Helpers/AesHelper.php", "modules/Core/app/Helpers/SignatureHelper.php"], "psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"], "dev": ["Composer\\Config::disableProcessTimeout", "npx concurrently -c \"#34d399,#a3e635,#8b5cf6,#6ee7b7\" \"php artisan optimize:clear\" \"php artisan reverb:start --debug\" \"meilisearch --no-analytics --master-key=VZ5P6PcUWMNqPEAAND5S0OJKmOAQILf5U_lb_w1Na8A\" \"php artisan queue:listen --queue=high,default,low,scout --tries=1 --timeout=900\"  --names=optimize,reverb,meilisearch,queue"], "queue": ["Composer\\Config::disableProcessTimeout", "php artisan queue:listen --queue=high,default,low,scout --tries=1 --timeout=900"], "reverb": ["Composer\\Config::disableProcessTimeout", "php artisan reverb:start --debug"], "test": ["@php artisan config:clear --ansi", "@php artisan test"]}, "extra": {"laravel": {"dont-discover": []}, "merge-plugin": {"include": ["modules/*/composer.json"]}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true, "wikimedia/composer-merge-plugin": true}}, "minimum-stability": "stable", "prefer-stable": true, "repositories": [{"type": "composer", "url": "https://nova.laravel.com"}]}