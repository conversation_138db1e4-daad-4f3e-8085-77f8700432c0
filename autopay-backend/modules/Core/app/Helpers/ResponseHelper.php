<?php

namespace Modules\Core\Helpers;

use Exception;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\ResponseBuilder\ResponseBuilder;
use <PERSON><PERSON>\LaravelData\Data;
use Symfony\Component\HttpFoundation\Response as HttpResponse;

class ResponseHelper
{
    public static function error($message = null, $data = null, ?int $httpCode = null, ?int $apiCode = null): HttpResponse
    {
        try {
            $rb = ResponseBuilder::asError($apiCode ?? $httpCode ?? HttpResponse::HTTP_BAD_REQUEST);
            // Convert data to array if it is a Data object
            if ($data instanceof Data) {
                $data = $data->toArray();
            }

            return $rb->withMessage($message)
                ->withData($data)
                ->withHttpCode($httpCode)
                ->build();
        } catch (Exception) {
            return response()->json([
                'code' => $apiCode ?? HttpResponse::HTTP_BAD_REQUEST,
                'message' => $data,
            ], HttpResponse::HTTP_BAD_REQUEST);
        }
    }

    public static function success($message = null, $data = null, ?int $httpCode = null, ?int $apiCode = null): HttpResponse
    {
        try {
            $rb = ResponseBuilder::asSuccess($apiCode ?? $httpCode ?? HttpResponse::HTTP_OK);
            // Convert data to array if it is a Data object
            if ($data instanceof Data) {
                $data = $data->toArray();
            }

            return $rb->withMessage($message)
                ->withData($data)
                ->withHttpCode($httpCode)
                ->build();
        } catch (Exception) {
            return response()->json([
                'code' => $apiCode ?? HttpResponse::HTTP_OK,
                'message' => $data,
            ], HttpResponse::HTTP_OK);
        }
    }
}
