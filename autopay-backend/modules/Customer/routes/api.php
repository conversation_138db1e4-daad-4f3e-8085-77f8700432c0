<?php

use Illuminate\Support\Facades\Route;
use Modules\Customer\Http\Controllers\Auth\ForgotPasswordController;
use Modules\Customer\Http\Controllers\Auth\LoginController;
use Modules\Customer\Http\Controllers\Auth\RegisterController;
use Modules\Customer\Http\Controllers\CustomerController;
use Modules\Organization\Http\Middleware\ResolveDomainOrganization;

Route::group([
    'middleware' => ResolveDomainOrganization::class,
], static function () {

    // Test route
    Route::get('/customer/test', function () {
        $organization = null;
        try {
            $organization = app('current.organization');
        } catch (\Exception $e) {
            // Organization isn't bound
        }

        return response()->json([
            'message' => 'Customer routes working',
            'organization' => $organization?->name ?? 'No organization',
            'timestamp' => now(),
        ]);
    });

    // Simple register test
    Route::post('/customer/register-test', static function (\Illuminate\Http\Request $request) {
        $organization = app('current.organization');

        return response()->json([
            'message' => 'Register test',
            'organization' => $organization?->name ?? 'No organization',
            'data' => $request->all(),
        ]);
    });

    // Register without FormRequest validation
    Route::post('/customer/register-simple', static function (\Illuminate\Http\Request $request) {
        $organization = app('current.organization');

        if (! $organization) {
            return response()->json(['error' => 'No organization found'], 400);
        }

        // Simple validation
        $request->validate([
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'password' => 'required|string|min:6',
        ]);

        // Create customer
        $customer = \Modules\Customer\Models\Customer::create([
            'organization_id' => $organization->id,
            'first_name' => $request->first_name,
            'last_name' => $request->last_name,
            'email' => $request->email,
            'phone' => $request->phone,
            'password' => \Illuminate\Support\Facades\Hash::make($request->password),
            'is_active' => true,
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Customer created successfully',
            'customer' => $customer,
        ]);
    });

    // Register with FormRequest but no guest middleware
    Route::post('/customer/register-no-middleware', [\Modules\Customer\Http\Controllers\Auth\RegisterController::class, 'register']);

    // Test auth
    Route::get('/customer/auth-test', function () {
        return response()->json([
            'message' => 'Auth test',
            'user' => auth('customer')->user(),
            'guard' => auth()->getDefaultDriver(),
            'authenticated' => auth('customer')->check(),
        ]);
    })->middleware('auth:customer');

    // Test token without auth middleware
    Route::get('/customer/token-test', function (\Illuminate\Http\Request $request) {
        $token = $request->bearerToken();
        $user = null;

        if ($token) {
            // Try to find token in database
            $accessToken = \Laravel\Sanctum\PersonalAccessToken::findToken($token);
            if ($accessToken) {
                $user = $accessToken->tokenable;
            }
        }

        return response()->json([
            'message' => 'Token test',
            'has_token' => ! empty($token),
            'token_preview' => $token ? substr($token, 0, 20).'...' : null,
            'user' => $user,
            'auth_customer' => auth('customer')->user(),
            'auth_check' => auth('customer')->check(),
        ]);
    });

    // Customer authentication routes (guest only)
    Route::group([
        'prefix' => 'customer',
    ], static function () {
        // Guest routes (no authentication required)
        Route::group([
            'middleware' => 'guest:customer',
        ], static function () {
            Route::post('/login', [LoginController::class, 'login'])->name('customer.login');
            Route::post('/register', [RegisterController::class, 'register'])->name('customer.register');
            Route::post('/forgot', [ForgotPasswordController::class, 'forgot'])->name('customer.forgot');
        });

        // Password reset routes (no guest middleware needed for reset link)
        Route::group([
            'controller' => ForgotPasswordController::class,
            'middleware' => 'throttle:5,1',
        ], static function () {
            Route::get('/password/reset/{id}/{hash}', 'passwordReset')->name('customer.password.reset')
                ->middleware(['signed:relative']);
        });

        // Authenticated customer routes
        Route::group([
            'middleware' => 'auth:customer',
        ], static function () {
            Route::post('/logout', [LoginController::class, 'logout'])->name('customer.logout');
            Route::get('/me', [LoginController::class, 'me'])->name('customer.me');

            // Customer dashboard and profile
            Route::get('/dashboard', [CustomerController::class, 'dashboard'])->name('customer.dashboard');
            Route::put('/profile', [CustomerController::class, 'updateProfile'])->name('customer.profile.update');
        });
    });
});
