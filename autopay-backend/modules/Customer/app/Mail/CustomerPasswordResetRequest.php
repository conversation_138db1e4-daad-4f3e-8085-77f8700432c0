<?php

namespace Modules\Customer\Mail;

use App\Mail\Traits\HasDomainBranding;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class CustomerPasswordResetRequest extends Mailable
{
    use Queueable, SerializesModels, HasDomainBranding;

    /**
     * Create a new message instance.
     */
    public function __construct(protected array $data) {}

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            subject: 'Yêu cầu đặt lại mật khẩu khách hàng',
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: 'customer::mail.auth.forgot.request',
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }

    /**
     * Build the message.
     */
    public function build(): self
    {
        $subject = 'Yêu cầu đặt lại mật khẩu khách hàng';

        // Get brand data from current domain
        $brandData = $this->getBrandData();

        return $this
            ->subject($subject)
            ->view('customer::mail.auth.forgot.request')
            ->with([
                'title' => $subject,
                'name' => $this->data['name'],
                'ip_address' => $this->data['ip_address'],
                'location' => $this->data['location'],
                'datetime' => $this->data['datetime'],
                'timezone' => $this->data['timezone'],
                'resetUrl' => $this->data['resetUrl'],
                ...$brandData,
            ]);
    }
}
