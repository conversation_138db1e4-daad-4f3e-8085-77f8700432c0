<?php

namespace Modules\Customer\Http\Requests\Auth;

use Illuminate\Foundation\Http\FormRequest;
use Modules\Customer\Models\Customer;
use Illuminate\Validation\Rule;

class RegisterRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'first_name' => ['required', 'string', 'max:255'],
            'last_name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'email', 'max:255'],
            'phone' => ['nullable', 'string', 'max:20'],
            'password' => ['required', 'string', 'min:6', 'confirmed'],
            'password_confirmation' => ['required', 'string', 'min:6'],
        ];
    }

    /**
     * Get custom validation messages.
     */
    public function messages(): array
    {
        return [
            'first_name.required' => 'Vui lòng nhập họ.',
            'first_name.string' => '<PERSON><PERSON> phải là chuỗi ký tự.',
            'first_name.max' => '<PERSON><PERSON> không được vượt quá 255 ký tự.',
            'last_name.required' => 'Vui lòng nhập tên.',
            'last_name.string' => 'Tên phải là chuỗi ký tự.',
            'last_name.max' => 'Tên không được vượt quá 255 ký tự.',
            'email.required' => 'Vui lòng nhập địa chỉ email.',
            'email.email' => 'Địa chỉ email không hợp lệ.',
            'email.max' => 'Địa chỉ email không được vượt quá 255 ký tự.',
            'email.unique' => 'Địa chỉ email này đã được sử dụng.',
            'phone.string' => 'Số điện thoại phải là chuỗi ký tự.',
            'phone.max' => 'Số điện thoại không được vượt quá 20 ký tự.',
            'password.required' => 'Vui lòng nhập mật khẩu.',
            'password.string' => 'Mật khẩu phải là chuỗi ký tự.',
            'password.min' => 'Mật khẩu phải có ít nhất 6 ký tự.',
            'password.confirmed' => 'Xác nhận mật khẩu không khớp.',
            'password_confirmation.required' => 'Vui lòng xác nhận mật khẩu.',
            'password_confirmation.string' => 'Xác nhận mật khẩu phải là chuỗi ký tự.',
            'password_confirmation.min' => 'Xác nhận mật khẩu phải có ít nhất 6 ký tự.',
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // Skip if there are already validation errors
            if ($validator->errors()->any()) {
                return;
            }

            try {
                // Get current organization from domain context
                $organization = app('current.organization');

                if (!$organization) {
                    $validator->errors()->add('email', 'Không thể xác định tổ chức từ domain này.');
                    return;
                }

                // Check if organization allows customer registration
                if (!$organization->is_active) {
                    $validator->errors()->add('email', 'Tổ chức này hiện không cho phép đăng ký.');
                    return;
                }

                // Check email uniqueness within organization
                $existingCustomer = Customer::where('organization_id', $organization->id)
                                          ->where('email', $this->email)
                                          ->first();

                if ($existingCustomer) {
                    $validator->errors()->add('email', 'Email này đã được sử dụng trong tổ chức.');
                }
            } catch (\Exception $e) {
                $validator->errors()->add('email', 'Có lỗi xảy ra khi xác thực dữ liệu.');
            }
        });
    }
}
