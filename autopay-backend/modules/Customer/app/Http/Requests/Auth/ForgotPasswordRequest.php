<?php

namespace Modules\Customer\Http\Requests\Auth;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class ForgotPasswordRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        $organization = app('current.organization');

        return [
            'email' => [
                'required',
                'email',
                Rule::exists('customers', 'email')->where(function ($query) use ($organization) {
                    $query->where('organization_id', $organization?->id)
                          ->whereNotNull('email_verified_at')
                          ->where('is_active', true);
                }),
            ],
        ];
    }

    /**
     * Get the validation messages that apply to the request.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'email.exists' => 'Không tìm thấy tài khoản khách hàng nào trong hệ thống tương ứng với email của bạn hoặc email của bạn chưa được xác thực.',
        ];
    }
}
