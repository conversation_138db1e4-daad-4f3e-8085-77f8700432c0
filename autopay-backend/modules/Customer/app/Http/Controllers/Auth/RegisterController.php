<?php

namespace Modules\Customer\Http\Controllers\Auth;

use Illuminate\Http\JsonResponse;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\Hash;
use Modules\Customer\Http\Requests\Auth\RegisterRequest;
use Modules\Customer\Models\Customer;

class RegisterController extends Controller
{
    /**
     * Handle customer registration request.
     */
    public function register(RegisterRequest $request): JsonResponse
    {
        $organization = app('current.organization');

        if (!$organization) {
            return response()->json([
                'success' => false,
                'message' => 'Không thể xác định tổ chức từ domain này.',
            ], 400);
        }

        if (!$organization->is_active) {
            return response()->json([
                'success' => false,
                'message' => 'Tổ chức này hiện không hoạt động.',
            ], 400);
        }

        // Create new customer
        $customer = Customer::create([
            'organization_id' => $organization->id,
            'first_name' => $request->first_name,
            'last_name' => $request->last_name,
            'email' => $request->email,
            'phone' => $request->phone,
            'password' => Hash::make($request->password),
            'is_active' => true,
        ]);

        // Create token for customer
        $token = $customer->createToken('customer-token')->plainTextToken;

        return response()->json([
            'success' => true,
            'message' => 'Đăng ký thành công.',
            'data' => [
                'customer' => [
                    'id' => $customer->id,
                    'first_name' => $customer->first_name,
                    'last_name' => $customer->last_name,
                    'full_name' => $customer->full_name,
                    'email' => $customer->email,
                    'phone' => $customer->phone,
                    'organization' => [
                        'id' => $organization->id,
                        'name' => $organization->name,
                    ],
                ],
                'token' => $token,
            ],
        ], 201);
    }
}
