<?php

namespace Modules\Customer\Http\Controllers\Auth;

use Illuminate\Http\JsonResponse;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\URL;
use Modules\Core\Helpers\ResponseHelper;
use Modules\Customer\Http\Requests\Auth\ForgotPasswordRequest;
use Modules\Customer\Jobs\CustomerPasswordReset;
use Modules\Customer\Models\Customer;

class ForgotPasswordController extends Controller
{
    /**
     * Handle customer forgot password request.
     */
    public function forgot(ForgotPasswordRequest $request): JsonResponse
    {
        $organization = app('current.organization');
        
        if (!$organization) {
            return ResponseHelper::error('Không thể xác định tổ chức từ domain này.', 400);
        }

        $customer = Customer::where('email', $request->email)
                          ->where('organization_id', $organization->id)
                          ->where('is_active', true)
                          ->first();

        if (!$customer) {
            return ResponseHelper::error('Không tìm thấy khách hàng.', 404);
        }

        $ip = $this->getIpAddress();
        $country = $this->getCountryFromIp($ip);

        // Get current domain for frontend URL
        $currentDomain = app('current.domain');
        $frontendUrl = $currentDomain ? "https://{$currentDomain->frontend_hostname}" : config('app.url');

        $resetUrl = sprintf('%s%s', $frontendUrl, URL::temporarySignedRoute('customer.password.reset', now()->addMinutes(60), [
            'id' => $customer->id,
            'hash' => Crypt::encryptString($customer->email),
        ], absolute: false));

        CustomerPasswordReset::dispatch([
            'name' => $customer->first_name . ' ' . $customer->last_name,
            'email' => $customer->email,
            'datetime' => date('Y/m/d H:i'),
            'timezone' => '(UTC)',
            'ip_address' => $ip,
            'location' => $country,
            'resetUrl' => $resetUrl,
        ]);

        return ResponseHelper::success('Vui lòng kiểm tra email để hoàn tất quá trình đặt lại mật khẩu.');
    }

    /**
     * Handle customer password reset.
     */
    public function passwordReset(string $id, string $hash): JsonResponse
    {
        $organization = app('current.organization');
        
        if (!$organization) {
            return ResponseHelper::error('Không thể xác định tổ chức từ domain này.', 400);
        }

        try {
            $email = Crypt::decryptString($hash);
        } catch (\Exception $e) {
            return ResponseHelper::error('Link đặt lại mật khẩu không hợp lệ.', 400);
        }

        $customer = Customer::where('id', $id)
                          ->where('email', $email)
                          ->where('organization_id', $organization->id)
                          ->where('is_active', true)
                          ->first();

        if (!$customer) {
            return ResponseHelper::error('Không tìm thấy khách hàng hoặc link đã hết hạn.', 404);
        }

        // Generate new password
        $newPassword = $this->generateRandomPassword();
        $customer->update(['password' => bcrypt($newPassword)]);

        // Send success email with new password
        CustomerPasswordReset::dispatch([
            'name' => $customer->first_name . ' ' . $customer->last_name,
            'email' => $customer->email,
            'newPassword' => $newPassword,
        ], 'success');

        return ResponseHelper::success('Mật khẩu đã được đặt lại thành công. Vui lòng kiểm tra email để nhận mật khẩu mới.');
    }

    /**
     * Get IP address from request.
     */
    private function getIpAddress(): string
    {
        $headers = [
            'HTTP_CF_CONNECTING_IP',
            'HTTP_CLIENT_IP',
            'HTTP_X_FORWARDED_FOR',
            'HTTP_X_FORWARDED',
            'HTTP_X_CLUSTER_CLIENT_IP',
            'HTTP_FORWARDED_FOR',
            'HTTP_FORWARDED',
            'REMOTE_ADDR'
        ];

        foreach ($headers as $header) {
            if (!empty($_SERVER[$header])) {
                $ips = explode(',', $_SERVER[$header]);
                return trim($ips[0]);
            }
        }

        return request()->ip() ?? '127.0.0.1';
    }

    /**
     * Get country from IP address.
     */
    private function getCountryFromIp(string $ip): string
    {
        // Simple implementation - in production you might want to use a proper GeoIP service
        if ($ip === '127.0.0.1' || $ip === 'localhost') {
            return 'Local';
        }

        return 'Unknown';
    }

    /**
     * Generate random password.
     */
    private function generateRandomPassword(int $length = 12): string
    {
        $characters = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*';
        $password = '';
        
        for ($i = 0; $i < $length; $i++) {
            $password .= $characters[random_int(0, strlen($characters) - 1)];
        }
        
        return $password;
    }
}
