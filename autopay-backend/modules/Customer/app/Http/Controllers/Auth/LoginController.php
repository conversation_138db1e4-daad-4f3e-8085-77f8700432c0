<?php

namespace Modules\Customer\Http\Controllers\Auth;

use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Modules\Customer\Http\Requests\Auth\LoginRequest;
use Modules\Customer\Models\Customer;

class LoginController extends Controller
{
    /**
     * Handle customer login request.
     */
    public function login(LoginRequest $request): JsonResponse
    {
        $organization = app('current.organization');

        if (!$organization) {
            return response()->json([
                'success' => false,
                'message' => 'Không thể xác định tổ chức từ domain này.',
            ], 400);
        }

        // Find customer in organization
        $customer = Customer::where('email', $request->email)
                          ->where('organization_id', $organization->id)
                          ->where('is_active', true)
                          ->first();

        if (!$customer || !Hash::check($request->password, $customer->password)) {
            return response()->json([
                'success' => false,
                'message' => 'Thông tin đăng nhập không chính xác.',
            ], 401);
        }

        // Create token for customer
        $token = $customer->createToken('customer-token')->plainTextToken;

        return response()->json([
            'success' => true,
            'message' => 'Đăng nhập thành công.',
            'data' => [
                'customer' => [
                    'id' => $customer->id,
                    'first_name' => $customer->first_name,
                    'last_name' => $customer->last_name,
                    'full_name' => $customer->full_name,
                    'email' => $customer->email,
                    'phone' => $customer->phone,
                    'organization' => [
                        'id' => $organization->id,
                        'name' => $organization->name,
                    ],
                ],
                'token' => $token,
            ],
        ]);
    }

    /**
     * Handle customer logout request.
     */
    public function logout(Request $request): JsonResponse
    {
        $customer = Auth::guard('customer')->user();

        if ($customer) {
            // Revoke current token
            $customer->currentAccessToken()->delete();
        }

        return response()->json([
            'success' => true,
            'message' => 'Đăng xuất thành công.',
        ]);
    }

    /**
     * Get current authenticated customer.
     */
    public function me(Request $request): JsonResponse
    {
        $customer = Auth::guard('customer')->user();
        $organization = app('current.organization');

        if (!$customer) {
            return response()->json([
                'success' => false,
                'message' => 'Chưa đăng nhập.',
            ], 401);
        }

        return response()->json([
            'success' => true,
            'data' => [
                'customer' => [
                    'id' => $customer->id,
                    'first_name' => $customer->first_name,
                    'last_name' => $customer->last_name,
                    'full_name' => $customer->full_name,
                    'email' => $customer->email,
                    'phone' => $customer->phone,
                    'organization' => [
                        'id' => $organization->id,
                        'name' => $organization->name,
                    ],
                ],
            ],
        ]);
    }
}
