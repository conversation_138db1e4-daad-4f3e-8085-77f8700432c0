<?php

namespace Modules\Customer\Http\Controllers;

use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rule;

class CustomerController extends Controller
{
    /**
     * Get customer dashboard data.
     */
    public function dashboard(Request $request): JsonResponse
    {
        $customer = Auth::guard('customer')->user();
        $organization = app('current.organization');

        if (!$customer || $customer->organization_id !== $organization->id) {
            return response()->json([
                'success' => false,
                'message' => 'Không có quyền truy cập.',
            ], 403);
        }

        return response()->json([
            'success' => true,
            'message' => 'Chào mừng đến với dashboard.',
            'data' => [
                'customer' => [
                    'id' => $customer->id,
                    'first_name' => $customer->first_name,
                    'last_name' => $customer->last_name,
                    'full_name' => $customer->full_name,
                    'email' => $customer->email,
                    'phone' => $customer->phone,
                ],
                'organization' => [
                    'id' => $organization->id,
                    'name' => $organization->name,
                ],
                'stats' => [
                    'total_transactions' => 0,
                    'total_amount' => 0,
                    'pending_transactions' => 0,
                ],
            ],
        ]);
    }

    /**
     * Update customer profile.
     */
    public function updateProfile(Request $request): JsonResponse
    {
        $customer = Auth::guard('customer')->user();
        $organization = app('current.organization');

        if (!$customer || $customer->organization_id !== $organization->id) {
            return response()->json([
                'success' => false,
                'message' => 'Không có quyền truy cập.',
            ], 403);
        }

        $request->validate([
            'first_name' => ['required', 'string', 'max:255'],
            'last_name' => ['required', 'string', 'max:255'],
            'phone' => ['nullable', 'string', 'max:20'],
            'current_password' => ['nullable', 'string', 'min:6'],
            'password' => ['nullable', 'string', 'min:6', 'confirmed'],
            'password_confirmation' => ['nullable', 'string', 'min:6'],
        ], [
            'first_name.required' => 'Vui lòng nhập họ.',
            'first_name.string' => 'Họ phải là chuỗi ký tự.',
            'first_name.max' => 'Họ không được vượt quá 255 ký tự.',
            'last_name.required' => 'Vui lòng nhập tên.',
            'last_name.string' => 'Tên phải là chuỗi ký tự.',
            'last_name.max' => 'Tên không được vượt quá 255 ký tự.',
            'phone.string' => 'Số điện thoại phải là chuỗi ký tự.',
            'phone.max' => 'Số điện thoại không được vượt quá 20 ký tự.',
            'current_password.string' => 'Mật khẩu hiện tại phải là chuỗi ký tự.',
            'current_password.min' => 'Mật khẩu hiện tại phải có ít nhất 6 ký tự.',
            'password.string' => 'Mật khẩu mới phải là chuỗi ký tự.',
            'password.min' => 'Mật khẩu mới phải có ít nhất 6 ký tự.',
            'password.confirmed' => 'Xác nhận mật khẩu mới không khớp.',
            'password_confirmation.string' => 'Xác nhận mật khẩu phải là chuỗi ký tự.',
            'password_confirmation.min' => 'Xác nhận mật khẩu phải có ít nhất 6 ký tự.',
        ]);

        // If changing password, verify current password
        if ($request->filled('password')) {
            if (!$request->filled('current_password')) {
                return response()->json([
                    'success' => false,
                    'message' => 'Vui lòng nhập mật khẩu hiện tại để thay đổi mật khẩu.',
                    'errors' => [
                        'current_password' => ['Vui lòng nhập mật khẩu hiện tại.']
                    ],
                ], 422);
            }

            if (!Hash::check($request->current_password, $customer->password)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Mật khẩu hiện tại không chính xác.',
                    'errors' => [
                        'current_password' => ['Mật khẩu hiện tại không chính xác.']
                    ],
                ], 422);
            }
        }

        // Update customer data
        $updateData = [
            'first_name' => $request->first_name,
            'last_name' => $request->last_name,
            'phone' => $request->phone,
        ];

        if ($request->filled('password')) {
            $updateData['password'] = Hash::make($request->password);
        }

        $customer->update($updateData);

        return response()->json([
            'success' => true,
            'message' => 'Cập nhật thông tin thành công.',
            'data' => [
                'customer' => [
                    'id' => $customer->id,
                    'first_name' => $customer->first_name,
                    'last_name' => $customer->last_name,
                    'full_name' => $customer->full_name,
                    'email' => $customer->email,
                    'phone' => $customer->phone,
                ],
            ],
        ]);
    }
}
