<?php

namespace Modules\Customer\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Mail;
use Modules\Customer\Mail\CustomerPasswordResetRequest;
use Modules\Customer\Mail\CustomerPasswordResetSuccess;

class CustomerPasswordReset implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     */
    public function __construct(
        protected array $data,
        protected string $type = 'request'
    ) {}

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        if ($this->type === 'success') {
            Mail::to($this->data['email'])->send(new CustomerPasswordResetSuccess($this->data));
        } else {
            Mail::to($this->data['email'])->send(new CustomerPasswordResetRequest($this->data));
        }
    }
}
