<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('domains', static function (Blueprint $table) {
            $table->ulid('id')->primary();
            $table->foreignUlid('organization_id')->nullable()->constrained('organizations')->cascadeOnDelete();

            // Domain information
            $table->string('frontend_hostname')->nullable(); // Frontend domain mapping
            $table->string('backend_hostname')->nullable(); // Backend domain mapping
            $table->string('status')->default('active'); // 'pending', 'active', 'failed', 'suspended'

            // Configuration data (consolidated from multiple fields)
            $table->json('data')->nullable(); // All configuration data in JSON format
            /*
             * data structure:
             * {
             *   "branding": {
             *     "name": "Custom brand name",
             *     "slogan": "Brand slogan",
             *     "email": "Contact email",
             *     "phone": "Contact phone",
             *     "logo_url": "Logo image URL",
             *     "favicon_url": "Favicon URL"
             *   },
             *   "theme": {
             *     "colors": { "primary": "#3b82f6", "secondary": "#64748b", ... },
             *     "custom_css": { ... }
             *   },
             *   "seo": {
             *     "title": "Meta title",
             *     "description": "Meta description",
             *     "keywords": "Meta keywords",
             *     "og_image": "Open Graph image URL"
             *   },
             *   "contact": { "phone": "...", "email": "...", "address": "..." }
             * }
             */

            // Status and settings
            $table->boolean('is_active')->default(true);

            $table->timestamps();

            // Indexes for performance
            $table->index(['frontend_hostname']);
            $table->index(['backend_hostname']);
            $table->index(['organization_id']);
            $table->index(['is_active']);
            $table->index(['status']);
            $table->index(['organization_id', 'status']);
            $table->index(['created_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('domains');
    }
};
