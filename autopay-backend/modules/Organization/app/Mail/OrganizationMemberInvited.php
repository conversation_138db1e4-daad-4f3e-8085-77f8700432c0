<?php

namespace Modules\Organization\Mail;

use App\Mail\Traits\HasDomainBranding;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Attachment;
use Illuminate\Queue\SerializesModels;

class OrganizationMemberInvited extends Mailable
{
    use Queueable, SerializesModels, HasDomainBranding;

    /**
     * Create a new message instance.
     */
    public function __construct(public $data)
    {
        //
    }

    /**
     * Build the message.
     */
    public function build(): static
    {
        $subject = "You have been invited to join {$this->data->organization_name}";

        // Get brand data from current domain
        $brandData = $this->getBrandData();

        return $this
            ->subject($subject)
            ->view('organization::emails.member-invited')
            ->with([
                'data' => $this->data,
                ...$brandData,
            ]);
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
