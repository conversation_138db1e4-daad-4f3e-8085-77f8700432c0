<?php

namespace Modules\Organization\Http\Requests;

use Modules\Core\Http\Requests\BaseFormRequest;

class SetupDomainRequest extends BaseFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'hostname' => 'required|string|max:255',
            'organization_id' => 'required|string|exists:organizations,id',
            'name' => 'nullable|string|max:255',
            'description' => 'nullable|string',
        ];
    }
}
