<?php

namespace Modules\Organization\Models;

use Illuminate\Database\Eloquent\Concerns\HasUlids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Domain extends Model
{
    use HasFactory, HasUlids;

    protected $fillable = [
        'organization_id',
        'frontend_hostname',
        'backend_hostname',
        'data',
        'is_active',
        'status',
    ];

    protected $casts = [
        'data' => 'array',
        'is_active' => 'boolean',
    ];

    /**
     * Set the data attribute with filtering for theme configuration.
     */
    public function setDataAttribute($value): void
    {
        if (is_array($value) && isset($value['theme']) && is_array($value['theme'])) {
            // Filter theme data to only allow 'name' field
            $value['theme'] = [
                'name' => $value['theme']['name'] ?? 'blue',
            ];
        }

        $this->attributes['data'] = json_encode($value);
    }

    /**
     * Get the organization that owns the domain.
     */
    public function organization(): BelongsTo
    {
        return $this->belongsTo(Organization::class);
    }



    /**
     * Scope to get active domains.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }



    /**
     * Get domain by frontend hostname.
     */
    public function scopeByHostname($query, string $hostname)
    {
        return $query->where('frontend_hostname', $hostname);
    }





    /**
     * Scope to get domains by status.
     */
    public function scopeByStatus($query, string $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope to get domains by organization.
     */
    public function scopeByOrganization($query, string $organizationId)
    {
        return $query->where('organization_id', $organizationId);
    }

    /**
     * Get the theme configuration.
     */
    public function getThemeConfigAttribute(): array
    {
        $themeData = $this->data['theme'] ?? [];

        return [
            'name' => $themeData['name'] ?? 'blue',
        ];
    }

    /**
     * Get the full branding configuration.
     */
    public function getBrandingConfigAttribute(): array
    {
        $brandingData = $this->data['branding'] ?? [];

        return [
            'name' => $brandingData['name'] ?? $this->organization->name ?? 'Autopay',
            'slogan' => $brandingData['slogan'] ?? null,
            'email' => $brandingData['email'] ?? null,
            'phone' => $brandingData['phone'] ?? null,
            'logo_url' => $brandingData['logo_url'] ?? null,
            'favicon_url' => $brandingData['favicon_url'] ?? null,
        ];
    }

    /**
     * Get the SEO configuration.
     */
    public function getSeoConfigAttribute(): array
    {
        $seoData = $this->data['seo'] ?? [];
        $brandingData = $this->data['branding'] ?? [];

        return [
            'title' => $seoData['title'] ?? $brandingData['name'] ?? $this->organization->name ?? 'Autopay',
            'description' => $seoData['description'] ?? null,
            'keywords' => $seoData['keywords'] ?? null,
            'og_image' => $seoData['og_image'] ?? null,
        ];
    }



    /**
     * Get the full domain configuration for frontend.
     */
    public function getConfigAttribute(): array
    {
        return [
            'id' => $this->id,
            'frontend_hostname' => $this->frontend_hostname,
            'backend_hostname' => $this->backend_hostname,
            'branding' => $this->branding_config,
            'theme' => $this->theme_config,
            'seo' => $this->seo_config,
            'contact' => $this->data['contact'] ?? [],
        ];
    }






    /**
     * Check if domain is active.
     */
    public function isActive(): bool
    {
        return $this->status === 'active' && $this->is_active;
    }



}
