<?php

namespace Modules\Team\Mail;

use App\Mail\Traits\HasDomainBranding;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class TeamMemberInvitationAccepted extends Mailable
{
    use Queueable, SerializesModels, HasDomainBranding;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct(protected $data) {}

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build(): static
    {
        $teamName = $this->data->team_name ?? $this->data->team;
        $subject = "Welcome to {$teamName} team!";

        // Get brand data from current domain
        $brandData = $this->getBrandData();

        return $this
            ->subject($subject)
            ->view('team::mail.team.accepted')
            ->with([
                'title' => $subject,
                'name' => $this->data->name,
                'team' => $teamName,
                'organization' => $this->data->organization_name ?? null,
                'role' => $this->data->role_name ?? null,
                'team_leader' => $this->data->team_leader ?? $this->data->invited_by_name,
                'team_leader_email' => $this->data->team_leader_email ?? $this->data->invited_by_email,
                'member_count' => $this->data->member_count ?? 'Multiple',
                'team_dashboard_url' => $this->data->team_dashboard_url ?? $brandData['frontendUrl'].'/dashboard',
                'support_url' => $this->data->support_url ?? $brandData['frontendUrl'].'/support',
                'datetime' => $this->data->datetime ?? now()->format('Y-m-d H:i:s'),
                ...$brandData,
                'timezone' => $this->data->timezone ?? '(UTC)',
            ]);
    }
}
