<x-email.layout
  :message="$message"
  :title="'Bạn đã bị xóa khỏi nhóm ' . $data->team_name"
  label="Xóa khỏi nhóm"
>
  <x-email.heading>
    Xin chào {{ $data->user_name }}!
  </x-email.heading>

  <x-email.paragraph>
    Chúng tôi viết thư để thông báo rằng bạn đã bị xóa khỏi nhóm <strong>{{ $data->team_name }}</strong> trong tổ chức <strong>{{ $data->organization_name }}</strong> trên {{ config('app.name') }}.
  </x-email.paragraph>

  <x-email.info-card>
    <x-email.info-title>Chi tiết xóa</x-email.info-title>
    <x-email.info-item label="Tổ chức">{{ $data->organization_name }}</x-email.info-item>
    <x-email.info-item label="Nhóm">{{ $data->team_name }}</x-email.info-item>
    <x-email.info-item label="Vai trò trước đây">{{ $data->role_name }}</x-email.info-item>
    <x-email.info-item label="Được xóa bởi">{{ $data->removed_by_name }} ({{ $data->removed_by_email }})</x-email.info-item>
    <x-email.info-item label="Thời gian xóa">{{ $data->removed_at }}</x-email.info-item>
  </x-email.info-card>

  @if($data->reason)
    <x-email.paragraph>
      <strong>Lý do:</strong><br>
      {{ $data->reason }}
    </x-email.paragraph>
  @endif

  <x-email.paragraph>
    Bạn không còn quyền truy cập vào tài nguyên của nhóm này. Tuy nhiên, bạn vẫn có thể truy cập các nhóm khác trong cùng tổ chức nếu bạn là thành viên của nhiều nhóm.
  </x-email.paragraph>

  @if($data->has_other_teams)
    <x-email.paragraph style="color: var(--x-success);">
      <strong>Tin tốt!</strong> Bạn vẫn là thành viên của các nhóm khác trong tổ chức này và có thể tiếp tục truy cập những tài nguyên đó.
    </x-email.paragraph>

    <div style="margin: 0 0 32px; text-align: center;">
      <x-email.button href="{{ $frontendUrl }}/organizations/{{ $data->organization_id }}/dashboard">
        Xem Bảng điều khiển Tổ chức
      </x-email.button>
    </div>
  @endif

  <x-email.divider />

  <x-email.paragraph style="color: var(--x-gray);">
    Nếu bạn có bất kỳ câu hỏi nào về việc xóa này, vui lòng liên hệ
    <a
      href="mailto:{{ $data->removed_by_email }}"
      class="hover-underline"
      style="color: var(--x-black); text-decoration: none;"
    >{{ $data->removed_by_name }}</a>
    hoặc
    <a
      href="mailto:{{ config('mail.from.address') }}"
      class="hover-underline"
      style="color: var(--x-black); text-decoration: none;"
    >đội hỗ trợ</a> của chúng tôi.
  </x-email.paragraph>
</x-email.layout>
