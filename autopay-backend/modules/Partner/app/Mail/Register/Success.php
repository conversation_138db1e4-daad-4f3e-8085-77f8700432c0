<?php

namespace Modules\Partner\Mail\Register;

use App\Mail\Traits\HasDomainBranding;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class Success extends Mailable
{
    use Queueable, SerializesModels, HasDomainBranding;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct(protected $data) {}

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        $subject = 'Register successfully!';

        // Get brand data from current domain
        $brandData = $this->getBrandData();

        return $this
            ->subject($subject)
            ->view('mail.partner.register.success')
            ->with([
                'title' => $subject,
                'name' => $this->data->name,
                'email' => $this->data->email,
                'password' => $this->data->password,
                'confirmUrl' => $this->data->confirmUrl ?? null,
                ...$brandData,
            ]);
    }
}
