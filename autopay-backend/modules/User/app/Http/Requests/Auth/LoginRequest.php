<?php

namespace Modules\User\Http\Requests\Auth;

use Illuminate\Validation\Rules\Password;
use Modules\Core\Http\Requests\BaseFormRequest;
use Modules\User\Models\DeletedUser;
use Modules\User\Models\User;


class LoginRequest extends BaseFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            'email' => [
                'bail',
                'required',
                'email:rfc,dns',
                'exists:users,email',
                function ($attribute, $value, $fail) {
                    $this->validateNotDeletedUser($value, $fail);
                },
                function ($attribute, $value, $fail) {
                    $this->validateEmailVerified($value, $fail);
                },
            ],
            'password' => [
                'bail',
                'required',
                Password::min(8)->letters()->numbers()->symbols()->mixedCase()->uncompromised(),
                function ($attribute, $value, $fail) {
                    $this->validateCredentials($this->all(), $fail);
                },
            ],
            'remember' => 'boolean',
        ];
    }

    public function messages(): array
    {
        return [
            'email.exists' => 'Email tài kho<PERSON>n của bạn không hợp lệ.',
        ];
    }

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Validate that the user is not deleted from the system
     */
    protected function validateNotDeletedUser(string $email, callable $fail): void
    {
        if (DeletedUser::where('email_hashed', md5($email))->exists()) {
            $fail('Người dùng này không hợp lệ.');
        }
    }

    /**
     * Validate that the user's email is verified
     */
    protected function validateEmailVerified(string $email, callable $fail): void
    {
        $user = User::whereEmail($email)->first();

        if (empty($user?->email_verified_at)) {
            $fail('Email của bạn chưa được xác thực. Vui lòng kiểm tra email để lấy liên kết xác thực.');
        }
    }

    /**
     * Validate user credentials
     */
    protected function validateCredentials(array $data, callable $fail): void
    {
        $user = User::where('email', $data['email'])->first();

        if (! $user || ! password_verify($data['password'], $user->password)) {
            $fail('Thông tin đăng nhập không chính xác.');
        }
    }
}
