<?php

namespace Modules\User\Http\Requests\Auth;

use Illuminate\Validation\Rules\Password;
use Illuminate\Validation\ValidationException;
use Modules\Core\Http\Requests\BaseFormRequest;
use Modules\User\Models\DeletedUser;

class PrecheckRequest extends BaseFormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            'first_name' => [
                'required',
                'string',
                'min:1',
                'max:50',
                'regex:/^[\p{L}\s\-\'\.]+$/u', // Letters, spaces, hyphens, apostrophes, dots
            ],
            'last_name' => [
                'required',
                'string',
                'min:1',
                'max:50',
                'regex:/^[\p{L}\s\-\'\.]+$/u', // Letters, spaces, hyphens, apostrophes, dots
            ],
            'phone' => [
                'required',
                'string',
                'min:10',
                'max:15',
                'regex:/^[0-9\+\-\s\(\)]+$/', // Numbers, plus, minus, spaces, parentheses
                'unique:users,phone',
            ],
            'email' => [
                'bail',
                'required',
                'email:rfc,dns',
                'unique:users,email',
            ],
            'password' => [
                'bail',
                'required',
                Password::min(8)->letters()->numbers()->symbols()->mixedCase()->uncompromised(),
            ],
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'first_name.required' => 'Họ và tên đệm không được để trống',
            'first_name.min' => 'Họ và tên đệm phải có ít nhất 1 ký tự',
            'first_name.max' => 'Họ và tên đệm không được vượt quá 50 ký tự',
            'first_name.regex' => 'Họ và tên đệm chỉ được chứa chữ cái, khoảng trắng, dấu gạch ngang, dấu nháy đơn và dấu chấm',
            'last_name.required' => 'Tên không được để trống',
            'last_name.min' => 'Tên phải có ít nhất 1 ký tự',
            'last_name.max' => 'Tên không được vượt quá 50 ký tự',
            'last_name.regex' => 'Tên chỉ được chứa chữ cái, khoảng trắng, dấu gạch ngang, dấu nháy đơn và dấu chấm',
            'phone.required' => 'Số điện thoại không được để trống',
            'phone.min' => 'Số điện thoại phải có ít nhất 10 ký tự',
            'phone.max' => 'Số điện thoại không được vượt quá 15 ký tự',
            'phone.regex' => 'Số điện thoại chỉ được chứa số, dấu cộng, dấu gạch ngang, khoảng trắng và dấu ngoặc đơn',
            'phone.unique' => 'Số điện thoại này đã được sử dụng',
        ];
    }

    /**
     * Handle a passed validation attempt.
     *
     *
     * @throws ValidationException
     */
    protected function passedValidation(): void
    {
        // Check if this user email was previously deleted from our system
        $isDeletedUser = DeletedUser::where('email_hashed', md5((string) $this->email))->exists();

        if ($isDeletedUser) {
            throw ValidationException::withMessages([
                'email' => 'This user account has been deleted and cannot be registered again.',
            ]);
        }
    }
}
