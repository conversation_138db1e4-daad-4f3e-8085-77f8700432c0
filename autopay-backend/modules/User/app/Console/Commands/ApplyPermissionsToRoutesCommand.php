<?php

namespace Modules\User\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;
use Modules\User\Services\PermissionService;

class ApplyPermissionsToRoutesCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'permission:apply-to-routes
                            {module? : Specific module to update}
                            {--dry-run : Show what would be changed without making changes}
                            {--force : Apply changes without confirmation}';

    /**
     * The console command description.
     */
    protected $description = 'Apply permissions to all module routes automatically';

    /**
     * Permission service instance
     */
    protected PermissionService $permissionService;

    /**
     * Module to permission mapping
     */
    protected array $modulePermissionMap = [
        'Server' => 'server',
        'Webapp' => 'webapp',
        'Organization' => 'organization',
        'Team' => 'team',
        'Backup' => 'backup',
        'Database' => 'server',
        'Cronjob' => 'server',
        'Supervisord' => 'server',
        'SystemUser' => 'server',
        'Sshkey' => 'server',
        'FtpAccount' => 'server',
        'Nginx' => 'server',
        'Php' => 'server',
        'Nodejs' => 'server',
        'Pm2' => 'server',
        'Redis' => 'server',
        'Mysql' => 'server',
        'Mariadb' => 'server',
        'Webserver' => 'server',
        'Domain' => 'domain',
        'Application' => 'webapp',
        'GitConnection' => 'webapp',
        'WordPress' => 'webapp',
        'DnsManager' => 'dns',
        'Cloudflare' => 'dns',
        'User' => 'organization',
        'Notification' => 'notification',
    ];

    /**
     * Route action to permission mapping
     */
    protected array $actionPermissionMap = [
        'index' => 'view',
        'show' => 'view',
        'list' => 'view',
        'create' => 'create',
        'store' => 'create',
        'edit' => 'update',
        'update' => 'update',
        'destroy' => 'delete',
        'delete' => 'delete',
        'manage' => 'manage',
        'configure' => 'configure',
        'deploy' => 'deploy',
        'install' => 'create',
        'uninstall' => 'delete',
        'start' => 'manage',
        'stop' => 'manage',
        'restart' => 'manage',
        'reload' => 'manage',
    ];

    /**
     * Create a new command instance.
     */
    public function __construct(PermissionService $permissionService)
    {
        parent::__construct();
        $this->permissionService = $permissionService;
    }

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('🔄 Starting route permissions application...');

        $module = $this->argument('module');
        $modules = $module ? [$module] : $this->getAvailableModules();

        if (empty($modules)) {
            $this->error('❌ No modules found to process.');

            return 1;
        }

        if (! $this->option('dry-run') && ! $this->option('force')) {
            if (! $this->confirm('This will update routes in '.count($modules).' module(s). Continue?')) {
                $this->info('Operation cancelled.');

                return 0;
            }
        }

        $totalUpdated = 0;
        foreach ($modules as $moduleName) {
            $updated = $this->processModule($moduleName);
            $totalUpdated += $updated;
        }

        if ($this->option('dry-run')) {
            $this->info("🔍 Dry run completed. {$totalUpdated} routes would be updated.");
        } else {
            $this->info("✅ Applied permissions to {$totalUpdated} routes across ".count($modules).' modules.');
        }

        return 0;
    }

    /**
     * Get available modules with routes
     */
    protected function getAvailableModules(): array
    {
        $modules = [];
        $modulesPath = base_path('modules');

        if (! File::exists($modulesPath)) {
            return [];
        }

        $directories = File::directories($modulesPath);

        foreach ($directories as $directory) {
            $moduleName = basename($directory);
            $routesPath = $directory.'/routes/api.php';

            if (File::exists($routesPath)) {
                $modules[] = $moduleName;
            }
        }

        return $modules;
    }

    /**
     * Process a specific module
     */
    protected function processModule(string $moduleName): int
    {
        $routesPath = base_path("modules/{$moduleName}/routes/api.php");

        if (! File::exists($routesPath)) {
            $this->warn("⚠️  Routes file not found for module: {$moduleName}");

            return 0;
        }

        $this->line("📁 Processing module: {$moduleName}");

        $content = File::get($routesPath);
        $originalContent = $content;

        // Apply permissions based on module type
        $updatedContent = $this->applyPermissionsToContent($content, $moduleName);

        if ($originalContent === $updatedContent) {
            $this->line("  ℹ️  No changes needed for {$moduleName}");

            return 0;
        }

        if (! $this->option('dry-run')) {
            File::put($routesPath, $updatedContent);
            $this->line("  ✅ Updated {$moduleName} routes");
        } else {
            $this->line("  🔍 Would update {$moduleName} routes");
        }

        return 1;
    }

    /**
     * Apply permissions to route content
     */
    protected function applyPermissionsToContent(string $content, string $moduleName): string
    {
        $permissionModule = $this->modulePermissionMap[$moduleName] ?? strtolower($moduleName);

        // Pattern to match Route definitions
        $patterns = [
            // Route::get('/path', [Controller::class, 'method'])
            '/Route::(get|post|put|patch|delete|resource)\s*\(\s*[\'"]([^\'"]*)[\'"]\s*,\s*\[([^\]]+)\]\s*\)/i',
            // Route::get('/path', 'method')
            '/Route::(get|post|put|patch|delete|resource)\s*\(\s*[\'"]([^\'"]*)[\'"]\s*,\s*[\'"]([^\'"]*)[\'"]\s*\)/i',
        ];

        foreach ($patterns as $pattern) {
            $content = preg_replace_callback($pattern, function ($matches) use ($permissionModule) {
                return $this->addPermissionToRoute($matches, $permissionModule);
            }, $content);
        }

        return $content;
    }

    /**
     * Add permission middleware to a route
     */
    protected function addPermissionToRoute(array $matches, string $permissionModule): string
    {
        $method = $matches[1];
        $path = $matches[2];
        $controller = $matches[3];

        // Extract action from controller or path
        $action = $this->extractActionFromRoute($controller, $path);
        $resource = $this->extractResourceFromPath($path);

        // Generate permission name
        $permission = $this->generatePermissionName($permissionModule, $resource, $action);

        // Check if permission exists
        if (! $this->permissionService->permissionExists($permission)) {
            return $matches[0]; // Return original if permission doesn't exist
        }

        // Check if middleware already exists
        if (strpos($matches[0], 'middleware') !== false) {
            return $matches[0]; // Already has middleware
        }

        // Add middleware
        $middlewareType = $this->getMiddlewareType($permissionModule, $path);
        $middleware = "->middleware('{$middlewareType}:{$permission}')";

        return $matches[0].$middleware;
    }

    /**
     * Extract action from controller or path
     */
    protected function extractActionFromRoute(string $controller, string $path): string
    {
        // Try to extract from controller method
        if (preg_match('/@(\w+)/', $controller, $matches)) {
            return $this->actionPermissionMap[$matches[1]] ?? $matches[1];
        }

        // Try to extract from path
        if (preg_match('/\/(create|edit|delete|update|manage)/', $path, $matches)) {
            return $this->actionPermissionMap[$matches[1]] ?? $matches[1];
        }

        // Default based on HTTP method
        $httpMethod = strtolower($method ?? 'get');

        return match ($httpMethod) {
            'get' => 'view',
            'post' => 'create',
            'put', 'patch' => 'update',
            'delete' => 'delete',
            default => 'view'
        };
    }

    /**
     * Extract resource from path
     */
    protected function extractResourceFromPath(string $path): string
    {
        // Remove parameters and get the main resource
        $path = preg_replace('/\{[^}]+\}/', '', $path);
        $parts = array_filter(explode('/', $path));

        // Get the last meaningful part
        $resource = end($parts);

        // Map common resource names
        $resourceMap = [
            'databases' => 'database',
            'cronjobs' => 'cronjob',
            'users' => 'systemuser',
            'keys' => 'ssh',
            'security' => 'security',
            'settings' => 'settings',
            'health' => 'health',
            'apps' => 'apps',
            'backups' => 'backup',
            'domains' => 'domain',
            'ssl' => 'ssl',
            'files' => 'files',
        ];

        return $resourceMap[$resource] ?? ($resource ?: 'instance');
    }

    /**
     * Generate permission name
     */
    protected function generatePermissionName(string $module, string $resource, string $action): string
    {
        return "{$module}:{$resource}:{$action}";
    }

    /**
     * Get appropriate middleware type
     */
    protected function getMiddlewareType(string $module, string $path): string
    {
        // Check if it's a resource-specific route
        if (preg_match('/\{organization\}\/\{team\}\/\{ip_address\}/', $path)) {
            return 'resource.access';
        }

        // Default to organization permission
        return 'organization.permission';
    }
}
