<?php

namespace Modules\User\Mail;

use App\Mail\Traits\HasDomainBranding;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class UserPasswordReset extends Mailable
{
    use Queueable, SerializesModels, HasDomainBranding;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct(protected $data) {}

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        $subject = 'Yêu cầu đặt lại mật khẩu';

        // Get brand data from current domain
        $brandData = $this->getBrandData();

        return $this
            ->subject($subject)
            ->view('user::mail.auth.forgot.request')
            ->with([
                'title' => $subject,
                'name' => $this->data->name,
                'ip_address' => $this->data->ip_address,
                'location' => $this->data->location,
                'datetime' => $this->data->datetime,
                'timezone' => $this->data->timezone,
                'resetUrl' => $this->data->resetUrl,
                ...$brandData,
            ]);
    }
}
