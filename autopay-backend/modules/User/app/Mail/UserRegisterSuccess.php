<?php

namespace Modules\User\Mail;

use App\Mail\Traits\HasDomainBranding;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class UserRegisterSuccess extends Mailable
{
    use Queueable, SerializesModels, HasDomainBranding;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct(protected $data) {}

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build(): static
    {
        $subject = 'Xác thực đăng ký';

        // Get brand data from current domain
        $brandData = $this->getBrandData();

        return $this
            ->subject($subject)
            ->view('user::mail.auth.register.success')
            ->with([
                'title' => $subject,
                'name' => $this->data->name,
                'confirmUrl' => $this->data->confirmUrl,
                ...$brandData,
            ]);
    }
}
