<?php

/**
 * Role Templates and Permission Groups Configuration
 *
 * This file defines predefined role templates and permission groups
 * for easy role creation and management in the system.
 */

return [

    // ========================================
    // ORGANIZATION LEVEL ROLE TEMPLATES
    // ========================================

    'organization_roles' => [

        'organization_owner' => [
            'name' => 'Owner',
            'description' => 'Full ownership and control of the organization',
            'permissions' => ['*'], // All permissions
            'level' => 'organization',
        ],

        'organization_admin' => [
            'name' => 'Administrator',
            'description' => 'Administrative access to organization resources',
            'permissions' => [
                // Organization management
                'organization:view',
                'organization:update',
                'organization:member:invite',
                'organization:member:remove',
                'organization:member:manage',
                'organization:settings:manage',
                'organization:resource:assign',
                'organization:resource:unassign',

                // Team management
                'team:view',
                'team:create',
                'team:update',
                'team:delete',
                'team:member:manage',
                'team:resource:assign',
                'team:resource:unassign',
                'team:admin',

                // Role management
                'role:view',
                'role:create',
                'role:update',
                'role:delete',
                'role:permission:assign',
                'role:permission:revoke',
                'role:manage'
            ],
            'level' => 'organization',
        ],

        'organization_manager' => [
            'name' => 'Manager',
            'description' => 'Management access to organization resources',
            'permissions' => [
                'organization:view',
                'team:view',
                'team:create',
                'team:update',
                'team:member:manage',
            ],
            'level' => 'organization',
        ],

        'organization_member' => [
            'name' => 'Member',
            'description' => 'Basic member access to assigned resources',
            'permissions' => [
                'organization:view',
                'team:view',
            ],
            'level' => 'organization',
        ],
    ],

    // ========================================
    // TEAM LEVEL ROLE TEMPLATES
    // ========================================

    'team_roles' => [

        'team_lead' => [
            'name' => 'Team Lead',
            'description' => 'Leadership role with team management capabilities',
            'permissions' => [
                'team:view',
                'team:update',
                'team:member:invite',
                'team:member:remove',
                'team:member:manage',
            ],
            'level' => 'team',
        ],

        'team_senior' => [
            'name' => 'Senior Team Member',
            'description' => 'Senior member with advanced operational access',
            'permissions' => [
                'team:view',
            ],
            'level' => 'team',
        ],

        'team_member' => [
            'name' => 'Team Member',
            'description' => 'Standard team member with operational access',
            'permissions' => [
                'team:view',
            ],
            'level' => 'team',
        ],

        'team_viewer' => [
            'name' => 'Team Viewer',
            'description' => 'Read-only access to team resources',
            'permissions' => [
                'team:view',
            ],
            'level' => 'team',
        ],
    ],

    // ========================================
    // PERMISSION GROUPS
    // ========================================

    'permission_groups' => [
        'team_management' => [
            'name' => 'Team Management',
            'description' => 'Team and member management',
            'permissions' => [
                'team:view',
                'team:update',
                'team:member:invite',
                'team:member:remove',
                'team:member:manage',
            ],
        ],
    ],
];
