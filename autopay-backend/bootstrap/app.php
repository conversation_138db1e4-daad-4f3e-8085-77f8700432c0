<?php

use Illuminate\Auth\Middleware\Authenticate;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;
use Illuminate\Routing\Middleware\SubstituteBindings;
use Modules\Core\Helpers\ResponseHelper;

use Modules\Core\Http\Middleware\ForceJsonResponse;
use Modules\Organization\Http\Middleware\EnsureOrganizationAccess;
use Modules\Organization\Http\Middleware\EnsureOrganizationMember;
use Modules\Organization\Http\Middleware\EnsureOrganizationOwner;
use Modules\Organization\Http\Middleware\EnsureOrganizationPermission;
use Modules\Organization\Http\Middleware\EnsureRoleBelongsToOrganization;
use Modules\Organization\Http\Middleware\EnsureTeamAndRoleBelongToOrganization;
use Modules\Team\Http\Middleware\EnsureTeamAccess;
use Modules\Team\Http\Middleware\EnsureTeamOwner;
use Modules\Team\Http\Middleware\SetTeamPermissionContext;
use Modules\User\Http\Middleware\EnsureResourceAccess;
use Modules\User\Http\Middleware\TwoFactorVerification;
use Spatie\ResponseCache\Middlewares\CacheResponse;
use Spatie\ResponseCache\Middlewares\DoNotCacheResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\HttpException;
use Symfony\Component\HttpKernel\Exception\MethodNotAllowedHttpException;
use Symfony\Component\Routing\Exception\RouteNotFoundException;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        api: __DIR__.'/../routes/api.php',
        commands: __DIR__.'/../routes/console.php',
        // ! this is required to enable broadcasting/auth route
        channels: __DIR__.'/../routes/channels.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware) {
        $middleware->alias([
            '2fa' => TwoFactorVerification::class,

            // Team middleware
            'team.access' => EnsureTeamAccess::class,
            'team.owner' => EnsureTeamOwner::class,
            'team.permission.context' => SetTeamPermissionContext::class,

            // Organization middleware
            'organization.access' => EnsureOrganizationAccess::class,
            'organization.owner' => EnsureOrganizationOwner::class,
            'organization.member' => EnsureOrganizationMember::class,
            'organization.permission' => EnsureOrganizationPermission::class,
            'role.belongs.organization' => EnsureRoleBelongsToOrganization::class,
            'resource.access' => EnsureResourceAccess::class,
            'team.role.organization' => EnsureTeamAndRoleBelongToOrganization::class,

            'doNotCacheResponse' => DoNotCacheResponse::class,
        ]);

        // Define middleware groups for common patterns
        $middleware->group('organization.standard', [
            'auth',
            '2fa',
            'organization.member',
        ]);

        $middleware->group('team.standard', [
            'auth',
            '2fa',
            'organization.member',
            'team.permission.context',
            'team.access',
        ]);

        $middleware->group('server.standard', [
            'auth',
            '2fa',
            'organization.member',
            'team.permission.context',
            'team.access',
        ]);

        // Set middleware priority to ensure team permission context is set before route model binding
        $middleware->priority([
            Authenticate::class,
            SetTeamPermissionContext::class,
            SubstituteBindings::class,
        ]);

        $middleware->api(append: [
            CacheResponse::class,
            ForceJsonResponse::class,
        ]);
    })
    ->withExceptions(function (Exceptions $exceptions) {
        $exceptions->dontReport([
            RouteNotFoundException::class,
        ]);

        $exceptions->render(function (MethodNotAllowedHttpException|RouteNotFoundException $e) {
            return ResponseHelper::error('You are not allow to access this resource.', httpCode: Response::HTTP_UNAUTHORIZED, apiCode: Response::HTTP_UNAUTHORIZED);
        });

        $exceptions->render(function (HttpException|JsonException $e) {
            $statusCode = method_exists($e, 'getStatusCode') ? $e->getStatusCode() : Response::HTTP_BAD_REQUEST;
            // TODO: Log the error to sentry
            $message = 'We unable to process your request, please try again later.';

            return ResponseHelper::error($message, httpCode: $statusCode, apiCode: $statusCode);
        });
    })->create();
