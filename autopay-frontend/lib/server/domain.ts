import { headers } from 'next/headers'
import type { DomainConfig } from '../types/domain'

export interface DomainConfigResult {
  config: DomainConfig | null
  shouldSetHeader: boolean
  error?: string
}

/**
 * Get domain configuration on the server side
 * Note: Core hostname (APP_URL) doesn't need domain config - handled by the client
 */
export async function getDomainConfig(): Promise<DomainConfig | null> {
  try {
    const headersList = await headers()

    const domainConfigHeader = headersList.get('x-domain-config')
    if (domainConfigHeader) {
      try {
        // Decode base64 header (encoded to handle Unicode characters)
        const decodedConfig = Buffer.from(domainConfigHeader, 'base64').toString('utf-8')
        return JSON.parse(decodedConfig)
      } catch (parseError) {
        console.warn('Failed to parse domain config from header:', parseError)
      }
    }
  } catch (error) {
    console.error('Error fetching domain config:', error)
  }
  return null
}

/**
 * Fetch domain config for middleware - centralized logic
 * Returns config and whether to set header
 */
export async function fetchDomainConfigForMiddleware(hostname: string): Promise<DomainConfigResult> {
  try {
    // Check if hostname matches core frontend URL
    const coreFrontendUrl = process.env.NEXT_PUBLIC_APP_URL
    const coreFrontendHost = coreFrontendUrl ? new URL(coreFrontendUrl).hostname : null

    // Skip for core hostname - no config needed
    if (hostname === coreFrontendHost) {
      return {
        config: null,
        shouldSetHeader: false,
      }
    }

    // Fetch from backend API for custom domain hostnames
    const apiResponse = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/domains/config?hostname=${hostname}`, {
      headers: {
        Accept: 'application/json',
      },
      // Add cache control for middleware requests
      next: { revalidate: 600 }, // Cache for 10 minutes
    })

    if (!apiResponse.ok) {
      return {
        config: null,
        shouldSetHeader: false,
        error: `Domain not supported: ${hostname}`,
      }
    }

    const data = await apiResponse.json()

    if (data.success && data.data) {
      return {
        config: data.data,
        shouldSetHeader: true,
      }
    } else {
      return {
        config: null,
        shouldSetHeader: false,
        error: `Domain configuration not found: ${hostname}`,
      }
    }
  } catch (error) {
    console.warn('Failed to fetch domain config:', error)
    return {
      config: null,
      shouldSetHeader: false,
      error: `Failed to fetch domain config: ${error instanceof Error ? error.message : 'Unknown error'}`,
    }
  }
}
