import { getDomainConfig } from '@/lib/server/domain'
import type { Metadata } from 'next'

/**
 * Generate dynamic metadata based on domain configuration
 * Falls back to process.env.NEXT_PUBLIC_APP_NAME for app domain
 */
export async function generateDynamicMetadata(pageTitle: string, description?: string): Promise<Metadata> {
  const domainConfig = await getDomainConfig()

  // Get app name from domain config or fallback to env variable
  const appName =
    domainConfig?.branding?.name || domainConfig?.seo?.title || process.env.NEXT_PUBLIC_APP_NAME || 'AutoPAY'

  // Generate full title
  const fullTitle = `${pageTitle} - ${appName}`

  // Use domain SEO description if available, otherwise use provided description
  const metaDescription = domainConfig?.seo?.description || description

  return {
    title: fullTitle,
    ...(metaDescription && { description: metaDescription }),
  }
}
