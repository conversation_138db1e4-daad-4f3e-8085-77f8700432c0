'use client'

import { useDomain } from './useDomain'

export type DomainType = 'app' | 'mapped'

export interface DomainInfo {
  type: DomainType
  hostname: string
  isAppDomain: boolean
  isMappedDomain: boolean
}

/**
 * Hook to detect domain type and provide domain information
 * - 'app': APP_URL domain (supports OAuth + email/password for User login)
 * - 'mapped': Custom mapped domain (only email/password for Customer login)
 *
 * Optimized version that uses server-side domain config to avoid flicker
 */
export function useDomainType(): DomainInfo {
  const { config } = useDomain()

  // If we have domain config, it means this is a mapped domain
  // If no config, it's the app domain (APP_URL)
  const isMappedDomain = !!config
  const isAppDomain = !isMappedDomain

  // Get hostname from config or fallback to client-side detection
  const hostname = config?.frontend_hostname || (typeof window !== 'undefined' ? window.location.hostname : '')

  return {
    type: isMappedDomain ? 'mapped' : 'app',
    hostname,
    isAppDomain,
    isMappedDomain,
  }
}
