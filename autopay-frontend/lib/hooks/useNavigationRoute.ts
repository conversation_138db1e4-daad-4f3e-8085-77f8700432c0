'use client'

import { routes } from '@/lib/config/routes'
import { MenuRoute } from '@/lib/types/menu-route'
import { cn } from '@/lib/utils/utils'
import { useSelectedLayoutSegments } from 'next/navigation'

export function useNavigationRoute() {
  const segments = useSelectedLayoutSegments()
  const parentPath = segments.at(0)
  const subPath = segments.at(1) ?? ''
  const currentPath = segments.join('/')
  const isCurrentPath = (route: MenuRoute): boolean => {
    // Exact path match
    if (currentPath === route.path) {
      return true
    }

    // Check if current path is in explicitly defined subPaths
    if (route.subPaths?.includes(currentPath)) {
      return true
    }

    // Check regex pattern match
    if (route.matchRouteRegex?.test(currentPath)) {
      return true
    }

    // Check if current path starts with route path (for nested routes)
    // This handles cases like settings/theme, settings/domains being active for "settings"
    if (route.path && currentPath.startsWith(route.path + '/')) {
      return true
    }

    return false
  }

  const currentPathClassName = (route: MenuRoute, extraClass: string | boolean = '') => {
    return cn(
      'flex items-center gap-2 px-3 py-2 text-sm transition-colors',
      isCurrentPath(route)
        ? 'dark:bg-primary-foreground text-primary hover:text-foreground rounded-lg bg-white font-medium shadow-sm dark:shadow-[0_1px_2px_#ffffff]'
        : 'text-muted-foreground hover:text-primary dark:hover:text-white',
      extraClass
    )
  }

  return {
    currentPathClassName,
    isCurrentPath,
    parentPath,
    subPath,
    currentPath,
    routes,
  }
}
