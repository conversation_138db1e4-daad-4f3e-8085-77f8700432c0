import Providers from '@/app/providers'
import Tooltip from '@/components/display/Tooltip'
import { ThemeProvider } from '@/components/theme-provider'
import { Toaster } from '@/components/ui/sonner'
import { DomainProvider } from '@/lib/contexts/domain-context'
import { getDomainConfig } from '@/lib/server/domain'
import { cn } from '@/lib/utils'
import '@/styles/globals.css'
import type { Metadata } from 'next'
import { NextIntlClientProvider } from 'next-intl'
import { getLocale, getMessages } from 'next-intl/server'
import { Manrope } from 'next/font/google'
import { cookies } from 'next/headers'
import React from 'react'

const font = Manrope({
  subsets: ['latin'],
})

// Generate dynamic metadata based on domain config
export async function generateMetadata(): Promise<Metadata> {
  const domainConfig = await getDomainConfig()

  // For root layout, use app name directly as title
  const appName =
    domainConfig?.branding?.name || domainConfig?.seo?.title || process.env.NEXT_PUBLIC_APP_NAME || 'AutoPAY'

  const description = domainConfig?.seo?.description || 'AutoPAY'

  return {
    title: appName,
    description,
  }
}

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  const locale = await getLocale()
  const messages = await getMessages()
  const domainConfig = await getDomainConfig()

  const cookieStore = await cookies()
  const isLeftNavigationMinimized = cookieStore.get('isLeftNavigationMinimized')?.value === 'true'

  // Get theme class from domain config
  const themeClass = domainConfig?.theme?.name ? `theme-${domainConfig.theme.name}` : ''

  return (
    <html
      lang={locale}
      suppressHydrationWarning>
      <body
        className={cn('overscroll-none text-sm antialiased', font.className, themeClass)}
        style={
          {
            '--sidebar-width': isLeftNavigationMinimized ? '64px' : '248px',
          } as React.CSSProperties
        }
        suppressHydrationWarning>
        <Providers>
          <DomainProvider initialConfig={domainConfig}>
            <NextIntlClientProvider messages={messages}>
              <ThemeProvider
                attribute="class"
                defaultTheme="system"
                enableSystem
                disableTransitionOnChange>
                {children}
                <Toaster
                  visibleToasts={1}
                  richColors={true}
                  position="top-center"
                  toastOptions={{
                    classNames: {
                      toast: 'py-2! w-fit! mx-auto! relative! -mt-3!',
                    },
                  }}
                />
                <Tooltip />
              </ThemeProvider>
            </NextIntlClientProvider>
          </DomainProvider>
        </Providers>
      </body>
    </html>
  )
}
