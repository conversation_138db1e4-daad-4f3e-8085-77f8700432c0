import { generateDynamicMetadata } from '@/lib/utils/metadata'
import type { Metadata } from 'next'
import { type ReactNode } from 'react'

export async function generateMetadata(): Promise<Metadata> {
  return generateDynamicMetadata('Confirm')
}

import Layout from '@/app/(authorization)/common/components/layout'

export default function Component({ children }: { children: ReactNode }) {
  return <Layout>{children}</Layout>
}
