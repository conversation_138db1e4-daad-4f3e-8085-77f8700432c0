import { generateDynamicMetadata } from '@/lib/utils/metadata'
import type { Metada<PERSON> } from 'next'
import { type ReactNode } from 'react'

export async function generateMetadata(): Promise<Metadata> {
  return generateDynamicMetadata('Quên mật khẩu')
}

import Layout from '@/app/(authorization)/common/components/layout'

export default function Component({ children }: { children: ReactNode }) {
  return <Layout>{children}</Layout>
}
