'use client'

import { But<PERSON> } from '@/components/ui/button'
import { useIsMobile } from '@/hooks/use-mobile'
import { useDomain } from '@/lib/hooks/useDomain'
import { useNavigationRoute } from '@/lib/hooks/useNavigationRoute'
import Image from 'next/image'
import Link from 'next/link'
import React, { useCallback, useEffect, useMemo, useState } from 'react'
import { BiSupport } from 'react-icons/bi'
import { IoAddCircleOutline } from 'react-icons/io5'
import { MdArrowBackIos, MdArrowForwardIos, MdKeyboardArrowRight } from 'react-icons/md'

import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
  CommandSeparator,
} from '@/components/ui/command'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'

import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion'
import { cn } from '@/lib/utils'
import { useStore } from '@/stores/store'
import { LuChevronsUpDown } from 'react-icons/lu'

import Logo from '@/assets/images/logo-black.png'
import { BorderBeam } from '@/components/ui/border-beam'
import { ScrollArea } from '@/components/ui/scroll-area'
import { CheckIcon, GalleryVerticalEnd } from 'lucide-react'
import { usePathname } from 'next/navigation'

const teams = [
  {
    value: 'next.js',
    label: 'TEAM HAS VERY LONG TITLE HERE LONG LONG LONG',
  },
  {
    value: 'INTERGROUP',
    label: 'INTERGROUP',
  },
  {
    value: 'INTERDATA',
    label: 'INTERDATA',
  },
  {
    value: 'nuxt.js',
    label: 'CODETAY',
  },
]

type ComponentProps = {
  isLeftNavigationMinimized: boolean
}

import Cookies from 'js-cookie'

export default function Component({ isLeftNavigationMinimized }: ComponentProps) {
  // Use isLeftNavigationMinimized on the first render
  // This is to prevent the sidebar from flickering when the page is refreshed
  const [isMinimized, setMinimized] = useState(isLeftNavigationMinimized)

  const { config } = useDomain()

  // Extract branding data from domain config
  const branding = config?.branding || {}
  const brandName = (branding as any)?.name || process.env.NEXT_PUBLIC_APP_NAME || 'AutoPAY'
  const logoUrl = (branding as any)?.logo_url

  useEffect(() => {
    // No depend on isMinimized from server props after the first render
    setMinimized(Cookies.get('isLeftNavigationMinimized') === 'true')
  }, [])

  const pathname = usePathname()
  const [currentPath, setCurrentPath] = useState(pathname)

  const isMobile = useIsMobile()
  const { currentPathClassName, routes, parentPath, isCurrentPath } = useNavigationRoute()

  const { defaultTeamId, setDefaultTeamId, setShowLeftNavigationOnMobile } = useStore()

  // Find the route label that contains the current path
  const currentRouteCategory = routes.find((category) => {
    if (!category.routes) {
      return category.path ? category.path.startsWith(parentPath ?? '') : false
    }

    return category.routes.some((route) => {
      return route.path ? route.path.startsWith(parentPath ?? '') : false
    })
  })

  const allCategoryRouteLabels = useMemo(() => routes.map((category) => category.label), [routes])

  const calculateCategoryRouteLabels = useCallback(() => {
    return isMinimized
      ? allCategoryRouteLabels
      : currentRouteCategory
        ? [currentRouteCategory.label]
        : allCategoryRouteLabels.slice(0, 2)
  }, [isMinimized, allCategoryRouteLabels, currentRouteCategory])

  // Init default active category route labels
  const [activeCategoryRouteLabels, setActiveCategoryRouteLabels] = useState(calculateCategoryRouteLabels())

  useEffect(() => {
    setActiveCategoryRouteLabels(calculateCategoryRouteLabels())
  }, [isMinimized, calculateCategoryRouteLabels])

  // Trigger when clicking on a category route
  const navigationOnValueChange = (value: string[]) => {
    setActiveCategoryRouteLabels(value)
  }

  const toggleNavMinimized = useCallback(() => {
    const newStateOfLeftNavigationMinimized = !(Cookies.get('isLeftNavigationMinimized') === 'true')

    Cookies.set('isLeftNavigationMinimized', String(newStateOfLeftNavigationMinimized))

    const rootLayoutElement = document.querySelector<HTMLElement>('body')
    rootLayoutElement?.style.setProperty('--sidebar-width', newStateOfLeftNavigationMinimized ? '64px' : '248px')

    setMinimized(newStateOfLeftNavigationMinimized)
  }, [])

  const [openTeamSelectionModal, setTeamSelectionModal] = useState(false)

  useEffect(() => {
    if (isMobile && currentPath !== pathname) {
      setCurrentPath(pathname)
      toggleNavMinimized()
      setShowLeftNavigationOnMobile(false)
    }
  }, [currentPath, isMobile, pathname, toggleNavMinimized, setShowLeftNavigationOnMobile])

  return (
    <>
      <nav className="bg-sidebar z-10 flex h-screen flex-col">
        <div>
          <Link
            href="/"
            className={cn(
              'flex h-14 items-center gap-2 border-b py-4',
              !isMinimized && 'px-4',
              isMinimized && 'justify-center px-2'
            )}>
            <div className="relative overflow-hidden rounded-lg px-2 py-0.5">
              <div>
                <Image
                  width="110"
                  src={logoUrl || Logo}
                  className="dark:invert-100"
                  alt={brandName}
                />
              </div>
              <BorderBeam
                size={25}
                borderWidth={3}
                duration={2}
                delay={10}
                // colorFrom="#0a53f6"
                // colorTo="#ffffff"
              />
            </div>
          </Link>

          <ScrollArea className="h-[85vh]">
            <div className={cn('flex flex-col gap-5', isMinimized ? 'px-2 py-0' : 'p-4')}>
              {!isMinimized && (
                <>
                  <Popover
                    open={openTeamSelectionModal}
                    onOpenChange={setTeamSelectionModal}>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        role="combobox"
                        aria-expanded={openTeamSelectionModal}
                        className="dark:hover:text-primary bg-red-50 hover:bg-red-50 dark:bg-indigo-600">
                        <div className="flex w-full items-center justify-between gap-1">
                          <div className="flex flex-col gap-1 truncate text-left">
                            <p className="truncate font-semibold">
                              {defaultTeamId ? teams.find((team) => team.value === defaultTeamId)?.label : 'Team...'}
                            </p>
                            {/*<span className="text-lg font-medium leading-6">*/}
                            {/*  7.112.024 đ*/}
                            {/*</span>*/}
                          </div>
                          <LuChevronsUpDown />
                        </div>
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent
                      className="p-0"
                      align="start"
                      side={isMobile ? 'bottom' : 'right'}
                      sideOffset={4}>
                      <Command>
                        <CommandInput
                          placeholder="Tìm nhóm..."
                          className="h-10"
                        />
                        <CommandList>
                          <CommandEmpty>Không có kết quả.</CommandEmpty>
                          <CommandGroup>
                            {teams.map((team) => (
                              <CommandItem
                                key={team.value}
                                value={team.value}
                                onSelect={(currentValue) => {
                                  setDefaultTeamId(currentValue === defaultTeamId ? '' : currentValue)
                                  setTeamSelectionModal(false)
                                }}
                                className={cn('py-3 font-medium', {
                                  'bg-accent': defaultTeamId === team.value,
                                })}>
                                <div className="flex size-6 items-center justify-center rounded-sm border">
                                  <GalleryVerticalEnd />
                                </div>
                                <span className="w-5/6 truncate">{team.label}</span>
                                <CheckIcon
                                  className={cn(
                                    'ml-auto h-4 w-4',
                                    defaultTeamId === team.value ? 'opacity-100' : 'opacity-0'
                                  )}
                                />
                              </CommandItem>
                            ))}
                            <CommandSeparator className="my-1" />
                            <CommandItem
                              key="add-team"
                              value="add-team"
                              className="flex items-center py-3">
                              <IoAddCircleOutline />
                              <span className="text-muted-foreground">Thêm nhóm mới</span>
                            </CommandItem>
                          </CommandGroup>
                        </CommandList>
                      </Command>
                    </PopoverContent>
                  </Popover>
                </>
              )}

              <Accordion
                type="multiple"
                className="flex flex-col gap-1"
                value={activeCategoryRouteLabels}
                onValueChange={navigationOnValueChange}>
                {routes.map((categoryRoute) => {
                  return (
                    <AccordionItem
                      key={categoryRoute.label}
                      value={categoryRoute.label}
                      className="flex flex-col gap-2 border-0">
                      <AccordionTrigger className={cn('p-1 font-semibold text-[#888E9E]', isMinimized && 'hidden')}>
                        {categoryRoute.label}
                      </AccordionTrigger>
                      {categoryRoute.routes && (
                        <AccordionContent asChild>
                          {categoryRoute.routes.map((route) => {
                            return (
                              <Link
                                key={route.name}
                                href={'/' + route.path}
                                className={currentPathClassName(route, isMinimized && 'justify-center')}
                                data-tooltip-html={categoryRoute.label + ' / ' + route.name}
                                data-tooltip-hidden={!isMinimized}>
                                <div className="flex gap-2">
                                  {React.cloneElement(<route.icon />, {
                                    className: 'size-5',
                                  })}
                                  <span className={cn(isMinimized ? 'hidden' : '')}>{route.name}</span>
                                </div>
                                {!isMinimized && isCurrentPath(route) && (
                                  <MdKeyboardArrowRight className="ml-auto size-4" />
                                )}
                              </Link>
                            )
                          })}
                        </AccordionContent>
                      )}
                    </AccordionItem>
                  )
                })}
              </Accordion>
            </div>
          </ScrollArea>
        </div>

        <div
          className={cn(
            'mt-auto flex items-center gap-4 py-4',
            isMinimized ? 'flex-col justify-center gap-2 px-2' : 'justify-between pl-3'
          )}>
          <Link
            href="/support"
            className="flex items-center gap-2"
            data-tooltip-html="Hỗ trợ"
            data-tooltip-hidden={!isMinimized}>
            <BiSupport className="h-5 w-5" />
            {!isMinimized && <span>Hỗ trợ</span>}
          </Link>
          <Button
            variant="ghost"
            onClick={() => toggleNavMinimized()}
            data-tooltip-html="Mở rộng"
            data-tooltip-hidden={!isMinimized}>
            {!isMinimized ? <MdArrowBackIos className="size-4" /> : <MdArrowForwardIos className="size-4" />}
          </Button>
        </div>
      </nav>
    </>
  )
}
