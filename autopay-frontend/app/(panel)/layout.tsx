import MainContent from '@/app/(panel)/common/components/main-content'
import MainNav from '@/app/(panel)/common/components/main-nav'
import { cookies } from 'next/headers'
import React from 'react'

export default async function Component({ children }: { children: React.ReactNode }) {
  const cookieStore = await cookies()
  const isLeftNavigationMinimized = cookieStore.get('isLeftNavigationMinimized')?.value === 'true'

  return (
    <>
      <div className="mb-10 flex min-h-[95vh] w-full pt-16">
        <MainNav isLeftNavigationMinimized={isLeftNavigationMinimized} />
        <MainContent>{children}</MainContent>
      </div>
      <p className="text-muted-foreground fixed right-4 bottom-2 z-50 text-xs">
        {process.env.NEXT_PUBLIC_APP_SEMANTIC_VERSION}
      </p>
    </>
  )
}
