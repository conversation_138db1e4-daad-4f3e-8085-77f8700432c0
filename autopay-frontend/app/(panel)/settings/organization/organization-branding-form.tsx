'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import React from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import { ImageUpload } from '@/components/custom-ui/image-upload';
import FormSkeleton from '@/components/display/FormSkeleton';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { useAuth } from '@/lib/hooks/useAuth';
import { queryFetchHelper } from '@/lib/utils/fetchHelper';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { AlertCircle, Globe } from 'lucide-react';
import { toast } from 'sonner';

const organizationBrandingSchema = z.object({
  branding: z.object({
    name: z.string().optional(),
    slogan: z.string().optional(),
    email: z.string().email('Email không hợp lệ').optional().or(z.literal('')),
    phone: z.string().optional(),
    logo_url: z.string().optional(),
    favicon_url: z.string().optional(),
  }),
});

const domainSetupSchema = z.object({
  frontend_hostname: z
    .string()
    .min(1, 'Frontend domain là bắt buộc')
    .refine((val) => {
      return /^[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/.test(
        val,
      );
    }, 'Định dạng frontend hostname không hợp lệ'),
  backend_hostname: z
    .string()
    .optional()
    .refine((val) => {
      if (!val) return true;
      return /^[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/.test(
        val,
      );
    }, 'Định dạng backend hostname không hợp lệ'),
  branding: z.object({
    name: z.string().optional(),
    slogan: z.string().optional(),
    email: z.string().email('Email không hợp lệ').optional().or(z.literal('')),
    phone: z.string().optional(),
    logo_url: z.string().optional(),
    favicon_url: z.string().optional(),
  }),
});

type OrganizationBrandingValues = z.infer<typeof organizationBrandingSchema>;
type DomainSetupValues = z.infer<typeof domainSetupSchema>;

const defaultValues: Partial<OrganizationBrandingValues> = {
  branding: {
    name: '',
    slogan: '',
    email: '',
    phone: '',
    logo_url: '',
    favicon_url: '',
  },
};

const defaultDomainSetupValues: Partial<DomainSetupValues> = {
  frontend_hostname: '',
  backend_hostname: '',
  branding: {
    name: '',
    slogan: '',
    email: '',
    phone: '',
    logo_url: '',
    favicon_url: '',
  },
};

export function OrganizationBrandingForm() {
  const { user, loading: authLoading } = useAuth();
  const queryClient = useQueryClient();

  // Get current domain data
  const {
    data: domain,
    isLoading: isDomainLoading,
    error: domainError,
  } = useQuery({
    queryKey: ['domain', user?.current_organization?.id],
    queryFn: async () => {
      if (!user?.current_organization?.id) {
        throw new Error('Không tìm thấy thông tin tổ chức');
      }
      const response = await queryFetchHelper(
        `/${user.current_organization.id}/domains`,
      );
      return response.data;
    },
    enabled: !!user?.current_organization?.id && !authLoading,
  });

  const form = useForm<OrganizationBrandingValues>({
    resolver: zodResolver(organizationBrandingSchema),
    defaultValues,
    mode: 'onChange',
  });

  const domainSetupForm = useForm<DomainSetupValues>({
    resolver: zodResolver(domainSetupSchema),
    defaultValues: defaultDomainSetupValues,
    mode: 'onChange',
  });

  // Update form values when domain data is loaded
  React.useEffect(() => {
    if (domain) {
      const brandingData = domain.data?.branding || {};

      form.reset({
        branding: {
          name: brandingData.name || '',
          slogan: brandingData.slogan || '',
          email: brandingData.email || '',
          phone: brandingData.phone || '',
          logo_url: brandingData.logo_url || '',
          favicon_url: brandingData.favicon_url || '',
        },
      });
    }
  }, [domain, form]);

  // Create domain with branding mutation
  const { mutate: createDomainWithBranding, isPending: isCreating } =
    useMutation({
      mutationFn: async (data: DomainSetupValues) => {
        if (!user?.current_organization?.id) {
          throw new Error('Không tìm thấy thông tin tổ chức');
        }

        const createData = {
          frontend_hostname: data.frontend_hostname,
          backend_hostname: data.backend_hostname,
          data: {
            branding: data.branding,
          },
        };

        return queryFetchHelper(`/${user.current_organization.id}/domains`, {
          method: 'POST',
          body: JSON.stringify(createData),
        });
      },
      onSuccess: (response) => {
        toast.success('Tạo domain và cập nhật thông tin branding thành công');

        // Update cache directly instead of invalidating to prevent data loss
        if (response?.data) {
          queryClient.setQueryData(
            ['domain', user?.current_organization?.id],
            response.data,
          );
        } else {
          // Fallback to invalidation if no response data
          queryClient.invalidateQueries({
            queryKey: ['domain', user?.current_organization?.id],
          });
        }
      },
      onError: (error: any) => {
        toast.error(error.message || 'Có lỗi xảy ra khi tạo domain');
      },
    });

  // Update domain mutation
  const { mutate: updateDomain, isPending: isUpdating } = useMutation({
    mutationFn: async (data: OrganizationBrandingValues) => {
      if (!user?.current_organization?.id || !domain?.id) {
        throw new Error('Không tìm thấy thông tin domain');
      }

      const currentData = domain.data || {};
      const currentBranding = currentData.branding || {};

      // Check for removed images and delete old files
      const filesToDelete = [];

      // Check logo removal
      if (currentBranding.logo_url && !data.branding.logo_url) {
        filesToDelete.push({ type: 'logo', url: currentBranding.logo_url });
      }

      // Check favicon removal
      if (currentBranding.favicon_url && !data.branding.favicon_url) {
        filesToDelete.push({
          type: 'favicon',
          url: currentBranding.favicon_url,
        });
      }

      // Delete old files if any
      if (filesToDelete.length > 0) {
        for (const file of filesToDelete) {
          try {
            await queryFetchHelper(
              `/${user.current_organization.id}/domains/delete-file`,
              {
                method: 'POST',
                body: JSON.stringify({ url: file.url, type: file.type }),
              },
            );
          } catch (error) {
            // Continue with update even if file deletion fails
          }
        }
      }

      const updateData = {
        data: {
          ...currentData,
          branding: {
            ...currentBranding,
            ...data.branding,
          },
        },
      };

      return queryFetchHelper(
        `/${user.current_organization.id}/domains/${domain.id}`,
        {
          method: 'PUT',
          body: JSON.stringify(updateData),
        },
      );
    },
    onSuccess: (response) => {
      toast.success('Cập nhật thông tin branding thành công');

      // Update cache directly instead of invalidating to prevent data loss
      if (response?.data) {
        queryClient.setQueryData(
          ['domain', user?.current_organization?.id],
          response.data,
        );
      } else {
        // Fallback to invalidation if no response data
        queryClient.invalidateQueries({
          queryKey: ['domain', user?.current_organization?.id],
        });
      }
    },
    onError: (error: any) => {
      toast.error(error.message || 'Có lỗi xảy ra khi cập nhật');
    },
  });

  async function onSubmit(data: OrganizationBrandingValues) {
    updateDomain(data);
  }

  async function onDomainSetupSubmit(data: DomainSetupValues) {
    createDomainWithBranding(data);
  }

  // Show loading skeleton while fetching domain data
  if (authLoading || isDomainLoading) {
    return (
      <div className="space-y-6">
        <FormSkeleton
          titleWidth="w-40"
          descriptionWidth="w-56"
          className="mb-6"
        />
        <FormSkeleton
          titleWidth="w-32"
          descriptionWidth="w-48"
          className="mb-6"
        />
        <FormSkeleton
          titleWidth="w-24"
          descriptionWidth="w-40"
          buttonWidth="w-32"
        />
      </div>
    );
  }

  // Show error if domain fetch failed (but not 404)
  if (domainError && domainError.code !== 404) {
    return (
      <div className="space-y-6">
        <div className="text-red-500">
          Lỗi khi tải dữ liệu domain: {domainError.message}
        </div>
      </div>
    );
  }

  // Show domain setup form if no domain data or 404 error
  if (!domain || domainError?.code === 404) {
    return (
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <div className="flex items-center gap-3">
              <Globe className="h-5 w-5 text-blue-500" />
              <div>
                <CardTitle>Cấu hình Domain & Branding</CardTitle>
                <CardDescription>
                  Tổ chức của bạn chưa có domain được cấu hình. Vui lòng thiết
                  lập domain để sử dụng white-label và cấu hình thông tin
                  branding.
                </CardDescription>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <Form {...domainSetupForm}>
              <form
                onSubmit={domainSetupForm.handleSubmit(onDomainSetupSubmit)}
                className="space-y-6"
              >
                {/* Domain Configuration Section */}
                <div className="space-y-4">
                  <div className="flex items-center gap-2">
                    <Globe className="h-4 w-4" />
                    <h3 className="text-lg font-medium">Cấu hình Domain</h3>
                  </div>

                  <div className="grid grid-cols-1 gap-4">
                    <FormField
                      control={domainSetupForm.control}
                      name="frontend_hostname"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Frontend Domain *</FormLabel>
                          <FormControl>
                            <Input
                              {...field}
                              placeholder="app.example.com hoặc frontend.autopay.vn"
                            />
                          </FormControl>
                          <FormDescription>
                            Domain cho ứng dụng frontend (customer interface)
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={domainSetupForm.control}
                      name="backend_hostname"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Backend Domain</FormLabel>
                          <FormControl>
                            <Input
                              {...field}
                              placeholder="api.example.com hoặc backend.autopay.vn"
                            />
                          </FormControl>
                          <FormDescription>
                            Domain cho API backend (admin/staff interface) - Tùy
                            chọn
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>

                {/* Branding Configuration Section */}
                <div className="space-y-4">
                  <div className="flex items-center gap-2">
                    <AlertCircle className="h-4 w-4" />
                    <h3 className="text-lg font-medium">Thông tin Branding</h3>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <FormField
                      control={domainSetupForm.control}
                      name="branding.name"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Tên thương hiệu</FormLabel>
                          <FormControl>
                            <Input {...field} placeholder="AutoPAY" />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={domainSetupForm.control}
                      name="branding.slogan"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Slogan</FormLabel>
                          <FormControl>
                            <Input
                              {...field}
                              placeholder="Thanh toán tự động thông minh"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <FormField
                      control={domainSetupForm.control}
                      name="branding.email"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Email liên hệ</FormLabel>
                          <FormControl>
                            <Input
                              {...field}
                              type="email"
                              placeholder="<EMAIL>"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={domainSetupForm.control}
                      name="branding.phone"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Số điện thoại</FormLabel>
                          <FormControl>
                            <Input
                              {...field}
                              type="tel"
                              placeholder="0123456789"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <div className="grid grid-cols-2 gap-6">
                    <FormField
                      control={domainSetupForm.control}
                      name="branding.logo_url"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Logo</FormLabel>
                          <FormControl>
                            <ImageUpload
                              value={field.value}
                              onChange={field.onChange}
                              onRemove={() => field.onChange('')}
                              placeholder="Upload logo (sẽ được resize tự động)"
                              accept="image/*"
                              maxSize={5}
                              uploadEndpoint="domains/upload"
                              uploadType="logo"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={domainSetupForm.control}
                      name="branding.favicon_url"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Favicon</FormLabel>
                          <FormControl>
                            <ImageUpload
                              value={field.value}
                              onChange={field.onChange}
                              onRemove={() => field.onChange('')}
                              placeholder="Upload favicon (sẽ được resize tự động)"
                              accept="image/*"
                              maxSize={2}
                              uploadEndpoint="domains/upload"
                              uploadType="favicon"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>

                <Button type="submit" size="sm" disabled={isCreating}>
                  {isCreating
                    ? 'Đang tạo domain...'
                    : 'Tạo Domain & Cấu hình Branding'}
                </Button>
              </form>
            </Form>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <Card>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="branding.name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Tên thương hiệu</FormLabel>
                    <FormControl>
                      <Input {...field} placeholder="AutoPAY" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="branding.slogan"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Slogan</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        placeholder="Thanh toán tự động thông minh"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="branding.email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Email liên hệ</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        type="email"
                        placeholder="<EMAIL>"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="branding.phone"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Số điện thoại</FormLabel>
                    <FormControl>
                      <Input {...field} type="tel" placeholder="0123456789" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-2 gap-6">
              <FormField
                control={form.control}
                name="branding.logo_url"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Logo</FormLabel>
                    <FormControl>
                      <ImageUpload
                        value={field.value}
                        onChange={field.onChange}
                        onRemove={() => field.onChange('')}
                        placeholder="Upload logo (sẽ được resize tự động)"
                        accept="image/*"
                        maxSize={5}
                        uploadEndpoint="domains/upload"
                        uploadType="logo"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="branding.favicon_url"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Favicon</FormLabel>
                    <FormControl>
                      <ImageUpload
                        value={field.value}
                        onChange={field.onChange}
                        onRemove={() => field.onChange('')}
                        placeholder="Upload favicon (sẽ được resize tự động)"
                        accept="image/*"
                        maxSize={2}
                        uploadEndpoint="domains/upload"
                        uploadType="favicon"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </CardContent>
        </Card>

        <Button type="submit" size="sm" disabled={isUpdating}>
          {isUpdating ? 'Đang cập nhật...' : 'Cập nhật thông tin'}
        </Button>
      </form>
    </Form>
  );
}
