import type { NextConfig } from 'next'

import createNextIntlPlugin from 'next-intl/plugin'
const withNextIntl = createNextIntlPlugin()

/** @type {import('next').NextConfig} */
const nextConfig: NextConfig = {
  reactStrictMode: false,
  poweredByHeader: false,
  images: {
    remotePatterns: [
      {
        hostname: 'images.unsplash.com',
      },
      {
        hostname: 'avatars.githubusercontent.com',
      },
      {
        hostname: 'vietqr.co',
      },
      {
        hostname: 'api.autopay.id',
      },
      {
        hostname: 'localhost',
      },
    ],
  },
  turbopack: {},
  experimental: {
    reactCompiler: process.env.NODE_ENV !== 'development',
    authInterrupts: true,
  },
  allowedDevOrigins: ['autopay.id', '*.autopay.id'],
  // async headers() {
  //     return [
  //         {
  //             source: "/:path*",
  //             headers: [
  //                 {
  //                     key: "Cross-Origin-Opener-Policy",
  //                     value: "same-origin",
  //                 },
  //                 // { // Restrict the embedding of the resource to same-origin contexts
  //                 //     key: "Cross-Origin-Embedder-Policy",
  //                 //     value: "require-corp",
  //                 // },
  //             ],
  //         },
  //     ];
  // },
  devIndicators: {
    position: 'bottom-right',
  },
}

export default withNextIntl(nextConfig)
